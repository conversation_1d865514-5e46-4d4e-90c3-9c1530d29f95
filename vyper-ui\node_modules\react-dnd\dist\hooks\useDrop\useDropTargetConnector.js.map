{"version": 3, "sources": ["../../../src/hooks/useDrop/useDropTargetConnector.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport { TargetConnector } from '../../internals/index.js'\nimport type { DropTargetOptions } from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\n\nexport function useDropTargetConnector(\n\toptions: DropTargetOptions,\n): TargetConnector {\n\tconst manager = useDragDropManager()\n\tconst connector = useMemo(\n\t\t() => new TargetConnector(manager.getBackend()),\n\t\t[manager],\n\t)\n\tuseIsomorphicLayoutEffect(() => {\n\t\tconnector.dropTargetOptions = options || null\n\t\tconnector.reconnect()\n\t\treturn () => connector.disconnectDropTarget()\n\t}, [options])\n\treturn connector\n}\n"], "names": ["useMemo", "TargetConnector", "useDragDropManager", "useIsomorphicLayoutEffect", "useDropTargetConnector", "options", "manager", "connector", "getBackend", "dropTargetOptions", "reconnect", "disconnectDropTarget"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO,CAAA;AAE/B,SAASC,eAAe,QAAQ,0BAA0B,CAAA;AAE1D,SAASC,kBAAkB,QAAQ,0BAA0B,CAAA;AAC7D,SAASC,yBAAyB,QAAQ,iCAAiC,CAAA;AAE3E,OAAO,SAASC,sBAAsB,CACrCC,OAA0B,EACR;IAClB,MAAMC,OAAO,GAAGJ,kBAAkB,EAAE;IACpC,MAAMK,SAAS,GAAGP,OAAO,CACxB,IAAM,IAAIC,eAAe,CAACK,OAAO,CAACE,UAAU,EAAE,CAAC;IAAA,EAC/C;QAACF,OAAO;KAAC,CACT;IACDH,yBAAyB,CAAC,IAAM;QAC/BI,SAAS,CAACE,iBAAiB,GAAGJ,OAAO,IAAI,IAAI;QAC7CE,SAAS,CAACG,SAAS,EAAE;QACrB,OAAO,IAAMH,SAAS,CAACI,oBAAoB,EAAE;QAAA,CAAA;KAC7C,EAAE;QAACN,OAAO;KAAC,CAAC;IACb,OAAOE,SAAS,CAAA;CAChB"}