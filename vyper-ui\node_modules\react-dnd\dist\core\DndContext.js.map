{"version": 3, "sources": ["../../src/core/DndContext.ts"], "sourcesContent": ["import type { DragDropManager } from 'dnd-core'\nimport { createContext } from 'react'\n\n/**\n * The React context type\n */\nexport interface DndContextType {\n\tdragDropManager: DragDropManager | undefined\n}\n\n/**\n * Create the React Context\n */\nexport const DndContext = createContext<DndContextType>({\n\tdragDropManager: undefined,\n})\n"], "names": ["createContext", "DndContext", "dragDropManager", "undefined"], "mappings": "AACA,SAASA,aAAa,QAAQ,OAAO,CAAA;AASrC;;GAEG,CACH,OAAO,MAAMC,UAAU,GAAGD,aAAa,CAAiB;IACvDE,eAAe,EAAEC,SAAS;CAC1B,CAAC,CAAA"}