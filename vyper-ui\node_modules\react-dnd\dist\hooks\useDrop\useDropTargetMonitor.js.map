{"version": 3, "sources": ["../../../src/hooks/useDrop/useDropTargetMonitor.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport { DropTargetMonitorImpl } from '../../internals/index.js'\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\n\nexport function useDropTargetMonitor<O, R>(): DropTargetMonitor<O, R> {\n\tconst manager = useDragDropManager()\n\treturn useMemo(() => new DropTargetMonitorImpl(manager), [manager])\n}\n"], "names": ["useMemo", "DropTargetMonitorImpl", "useDragDropManager", "useDropTargetMonitor", "manager"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO,CAAA;AAE/B,SAASC,qBAAqB,QAAQ,0BAA0B,CAAA;AAEhE,SAASC,kBAAkB,QAAQ,0BAA0B,CAAA;AAE7D,OAAO,SAASC,oBAAoB,GAAkC;IACrE,MAAMC,OAAO,GAAGF,kBAAkB,EAAE;IACpC,OAAOF,OAAO,CAAC,IAAM,IAAIC,qBAAqB,CAACG,OAAO,CAAC;IAAA,EAAE;QAACA,OAAO;KAAC,CAAC,CAAA;CACnE"}