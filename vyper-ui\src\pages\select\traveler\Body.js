import React from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import DragIndicatorIcon from "@material-ui/icons/DragIndicator";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";
import { Operation } from "./Operation";
import { AddRequiredButton } from "./buttons/AddRequiredButton";
import { Traveler } from './Traveler';
import { useDrop, useDrag } from "react-dnd";

const useStyles = makeStyles({
  dragHandle: {
    display: "inline-flex",
    alignItems: "center",
    cursor: "grab",
    color: "#666",
    marginRight: "8px",
    "&:hover": {
      color: "#333",
    },
    "&:active": {
      cursor: "grabbing",
    },
  },
  operationWithDrag: {
    display: "flex",
    alignItems: "flex-start",
  },
  operationContent: {
    flex: 1,
  },
  draggingItem: {
    backgroundColor: "#f0f8ff",
    borderRadius: "4px",
    boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
    transition: "background-color 0.2s ease, box-shadow 0.2s ease",
  }
});

// DraggableOperation component for react-dnd
const DraggableOperation = ({
  operation,
  index,
  isDragDropEnabled,
  moveOperation,
  vyper,
  build,
  options,
  onEditOperation,
  onAddOperation,
  onRemoveOperation,
  onEditComponentName,
  onEditComponentValue,
  onAddComponent,
  onRemoveComponent,
  onClickValidate,
  onCommentOperation,
  approvedGroups,
  onUndoDelete,
  onSelectDiagramApproval,
  unApprovedGroups,
  onComponentReorder
}) => {
  const classes = useStyles();

  const [{ isDragging }, drag, preview] = useDrag({
    type: 'operation',
    item: { index, operation },
    canDrag: isDragDropEnabled,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'operation',
    hover: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveOperation(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
  });

  const determineValidatedOperation = (operation) => {
    return build.validatedOperations.find(
      (vo) => vo.operation === operation.name
    );
  };

  return (
    <div ref={(node) => drag(drop(node))} style={{ opacity: isDragging ? 0.5 : 1 }}>
      <div className={clsx(
        classes.operationWithDrag,
        isDragging && classes.draggingItem
      )}>
        {isDragDropEnabled && (
          <div className={classes.dragHandle}>
            <DragIndicatorIcon fontSize="small" />
          </div>
        )}
        <div className={classes.operationContent}>
          <Operation
            vyper={vyper}
            build={build}
            operation={operation}
            operationIndex={index}
            options={options}
            onEditOperation={(o) => onEditOperation(o, index)}
            onAddOperation={(o) => onAddOperation(o, index)}
            onRemoveOperation={(o) => onRemoveOperation(o, index)}
            onEditComponentName={(o, c, cPos) =>
              onEditComponentName(o, c, index, cPos)
            }
            onEditComponentValue={(o, c, cPos) =>
              onEditComponentValue(o, c, index, cPos)
            }
            onAddComponent={(o, c, cPos) => onAddComponent(o, c, index, cPos)}
            onRemoveComponent={(o, c, cPos) => onRemoveComponent(o, c, index, cPos)}
            validatedOperation={determineValidatedOperation(operation)}
            onClickValidate={onClickValidate}
            onCommentOperation={onCommentOperation}
            approvedGroups={approvedGroups}
            onUndoDelete={(o) => onUndoDelete(o)}
            reworkedTraveler={build?.reworkedTraveler}
            onSelectDiagramApproval={onSelectDiagramApproval}
            unApprovedGroups={unApprovedGroups}
            isDragDropEnabled={isDragDropEnabled}
            onComponentReorder={onComponentReorder}
          />
        </div>
      </div>
    </div>
  );
};

export const Body = ({
  vyper,
  build,
  options,
  onEditOperation,
  onAddOperation,
  onRemoveOperation,
  onEditComponentName,
  onEditComponentValue,
  onAddComponent,
  onRemoveComponent,
  onClickValidate,
  onCommentOperation,
  approvedGroups,
  onUndoDelete,
  onSelectDiagramApproval,
  unApprovedGroups,
  dragDropChanges,
  onOperationReorder,
  onComponentReorder,
}) => {
  const determineValidatedOperation = (operation) => {
    return build.validatedOperations.find(
      (vo) => vo.operation === operation.name
    );
  };

  // Use drag drop changes if available, otherwise use build operations
  const operationsToRender = dragDropChanges?.operations || build.traveler.operations;

  const operations = operationsToRender.filter(
    (operation) => operation.name !== "TEST"
  );

  const isDragDropEnabled = options.dragdrop;

  // Move operation function for drag and drop
  const moveOperation = (dragIndex, hoverIndex) => {
    if (dragIndex === hoverIndex) return;

    // Create a result object similar to react-beautiful-dnd
    const result = {
      source: { index: dragIndex, droppableId: "operations" },
      destination: { index: hoverIndex, droppableId: "operations" }
    };

    onOperationReorder(result);
  };

  const classes = useStyles();

  const isDevicePkgNiche = build?.templateSource?.templateType === 'DEVICE_PKGNICHE' || build?.templateSource?.templateType == undefined


  const handleSectionClick = (sectionName) => {
    const sectionElement = document.getElementById(sectionName);
    if (sectionElement) {
      const rect = sectionElement.getBoundingClientRect();
      const offset = rect.top + window.scrollY - document.querySelector('.optionBar').offsetHeight - 50;
      window.scrollTo({ top: offset, behavior: "smooth" });
    }
  };


  const renderOperations = () => {
    return operations.map((operation, n) => (
      <DraggableOperation
        key={operation.name}
        operation={operation}
        index={n}
        isDragDropEnabled={isDragDropEnabled}
        moveOperation={moveOperation}
        vyper={vyper}
        build={build}
        options={options}
        onEditOperation={onEditOperation}
        onAddOperation={onAddOperation}
        onRemoveOperation={onRemoveOperation}
        onEditComponentName={onEditComponentName}
        onEditComponentValue={onEditComponentValue}
        onAddComponent={onAddComponent}
        onRemoveComponent={onRemoveComponent}
        onClickValidate={onClickValidate}
        onCommentOperation={onCommentOperation}
        approvedGroups={approvedGroups}
        onUndoDelete={onUndoDelete}
        onSelectDiagramApproval={onSelectDiagramApproval}
        unApprovedGroups={unApprovedGroups}
        onComponentReorder={onComponentReorder}
      />
    ));
  };

  // When drag and drop is enabled, hide the traveller sidebar
  if (isDragDropEnabled) {
    return (
      <DndProvider backend={HTML5Backend}>
        <div>
          {options.editbutton ? (
            <AddRequiredButton
              title="Add Operation"
              onClick={() => onAddOperation(null)}
            />
          ) : null}

          <div>
            {renderOperations()}
          </div>
        </div>
      </DndProvider>
    );
  }

  // When drag and drop is disabled, show the traveller sidebar
  return (
    <div style={{ display: 'flex', flexDirection: 'row' }}>
       <div style={{
        width: '250px',
        height: '50vh',
        position: 'sticky',
        fontSize: '0.8rem',
        top: isDevicePkgNiche ? '300px' : '375px',
        left: '0',
        zIndex: 1,
        overflowY: 'auto',
        border: '1px solid #ccc',
        borderBottom: '1 px solid #ccc',
        padding: '20px',
        paddingBottom: '10%',
        boxSizing: 'border-box',
        flexShrink: 0,
      } }>
        <h2>Operations</h2>
        <ul>
          {operations.map((operation, index) => (
            <li key={index}>
              <a style={{ color: "blue" }}
                href={`#operation-${index}`}
                onClick={() => handleSectionClick(`operation-${index}`)}
              >
                {operation.name}
              </a>
            </li>
          ))}
        </ul>
      </div>
      <div style={{ paddingLeft: '40px', flex: 1, minWidth: '0', overflowX: 'auto' }}>
        <Traveler vyper={vyper} build={build} options={options} />
        {options.editbutton ? (
          <AddRequiredButton
            title="Add Operation"
            onClick={() => onAddOperation(null)}
          />
        ) : null}

        {operations.map((operation, index) => (
          <div id={`operation-${index}`} key={index} style={{ scrollMarginTop: isDevicePkgNiche ? '256px' : '327px', }}>
            <Operation
              vyper={vyper}
              build={build}
              operation={operation}
              operationIndex={index}
              options={options}
              onEditOperation={(o) => onEditOperation(o, index)}
              onAddOperation={(o) => onAddOperation(o, index)}
              onRemoveOperation={(o) => onRemoveOperation(o, index)}
              onEditComponentName={(o, c, cPos) =>
                onEditComponentName(o, c, index, cPos)
              }
              onEditComponentValue={(o, c, cPos) =>
                onEditComponentValue(o, c, index, cPos)
              }
              onAddComponent={(o, c, cPos) => onAddComponent(o, c, index, cPos)}
              onRemoveComponent={(o, c, cPos) => onRemoveComponent(o, c, index, cPos)}
              validatedOperation={determineValidatedOperation(operation)}
              onClickValidate={onClickValidate}
              onCommentOperation={onCommentOperation}
              approvedGroups={approvedGroups}
              onUndoDelete={(o) => onUndoDelete(o)}
              reworkedTraveler={build?.reworkedTraveler}
              onSelectDiagramApproval={onSelectDiagramApproval}
              unApprovedGroups={unApprovedGroups}
            />
          </div>

        ))}
      </div>
    </div>
  )


};