{"version": 3, "sources": ["../../../src/hooks/useDrop/DropTargetImpl.ts"], "sourcesContent": ["import type { DropTarget } from 'dnd-core'\n\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport type { DropTargetHookSpec } from '../types.js'\n\nexport class DropTargetImpl<O, R, P> implements DropTarget {\n\tpublic constructor(\n\t\tpublic spec: DropTargetHookSpec<O, R, P>,\n\t\tprivate monitor: DropTargetMonitor<O, R>,\n\t) {}\n\n\tpublic canDrop() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\treturn spec.canDrop ? spec.canDrop(monitor.getItem(), monitor) : true\n\t}\n\n\tpublic hover() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tif (spec.hover) {\n\t\t\tspec.hover(monitor.getItem(), monitor)\n\t\t}\n\t}\n\n\tpublic drop() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tif (spec.drop) {\n\t\t\treturn spec.drop(monitor.getItem(), monitor)\n\t\t}\n\t\treturn\n\t}\n}\n"], "names": ["DropTargetImpl", "canDrop", "spec", "monitor", "getItem", "hover", "drop"], "mappings": "AAKA,OAAO,MAAMA,cAAc;IAM1B,AAAOC,OAAO,GAAG;QAChB,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;QAC5B,OAAOD,IAAI,CAACD,OAAO,GAAGC,IAAI,CAACD,OAAO,CAACE,OAAO,CAACC,OAAO,EAAE,EAAED,OAAO,CAAC,GAAG,IAAI,CAAA;KACrE;IAED,AAAOE,KAAK,GAAG;QACd,MAAMH,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;QAC5B,IAAID,IAAI,CAACG,KAAK,EAAE;YACfH,IAAI,CAACG,KAAK,CAACF,OAAO,CAACC,OAAO,EAAE,EAAED,OAAO,CAAC;SACtC;KACD;IAED,AAAOG,IAAI,GAAG;QACb,MAAMJ,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;QAC5B,IAAID,IAAI,CAACI,IAAI,EAAE;YACd,OAAOJ,IAAI,CAACI,IAAI,CAACH,OAAO,CAACC,OAAO,EAAE,EAAED,OAAO,CAAC,CAAA;SAC5C;QACD,OAAM;KACN;IA1BD,YACQD,IAAiC,EAChCC,OAAgC,CACvC;aAFMD,IAAiC,GAAjCA,IAAiC;aAChCC,OAAgC,GAAhCA,OAAgC;KACrC;CAwBJ"}