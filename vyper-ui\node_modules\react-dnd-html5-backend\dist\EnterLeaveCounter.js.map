{"version": 3, "sources": ["../src/EnterLeaveCounter.ts"], "sourcesContent": ["import { union, without } from './utils/js_utils.js'\n\ntype NodePredicate = (node: Node | null | undefined) => boolean\n\nexport class EnterLeaveCounter {\n\tprivate entered: any[] = []\n\tprivate isNodeInDocument: NodePredicate\n\n\tpublic constructor(isNodeInDocument: NodePredicate) {\n\t\tthis.isNodeInDocument = isNodeInDocument\n\t}\n\n\tpublic enter(enteringNode: EventTarget | null): boolean {\n\t\tconst previousLength = this.entered.length\n\n\t\tconst isNodeEntered = (node: Node): boolean =>\n\t\t\tthis.isNodeInDocument(node) &&\n\t\t\t(!node.contains || node.contains(enteringNode as Node))\n\n\t\tthis.entered = union(this.entered.filter(isNodeEntered), [enteringNode])\n\n\t\treturn previousLength === 0 && this.entered.length > 0\n\t}\n\n\tpublic leave(leavingNode: EventTarget | null): boolean {\n\t\tconst previousLength = this.entered.length\n\n\t\tthis.entered = without(\n\t\t\tthis.entered.filter(this.isNodeInDocument),\n\t\t\tleavingNode,\n\t\t)\n\n\t\treturn previousLength > 0 && this.entered.length === 0\n\t}\n\n\tpublic reset(): void {\n\t\tthis.entered = []\n\t}\n}\n"], "names": ["union", "without", "EnterLeave<PERSON><PERSON>nter", "enter", "enteringNode", "<PERSON><PERSON><PERSON><PERSON>", "entered", "length", "isNodeEntered", "node", "isNodeInDocument", "contains", "filter", "leave", "leavingNode", "reset"], "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,QAAQ,qBAAqB,CAAA;AAIpD,OAAO,MAAMC,iBAAiB;IAQ7B,AAAOC,KAAK,CAACC,YAAgC,EAAW;QACvD,MAAMC,cAAc,GAAG,IAAI,CAACC,OAAO,CAACC,MAAM;QAE1C,MAAMC,aAAa,GAAG,CAACC,IAAU,GAChC,IAAI,CAACC,gBAAgB,CAACD,IAAI,CAAC,IAC3B,CAAC,CAACA,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,CAACP,YAAY,CAAS,CAAC;QAAA;QAExD,IAAI,CAACE,OAAO,GAAGN,KAAK,CAAC,IAAI,CAACM,OAAO,CAACM,MAAM,CAACJ,aAAa,CAAC,EAAE;YAACJ,YAAY;SAAC,CAAC;QAExE,OAAOC,cAAc,KAAK,CAAC,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,CAAA;KACtD;IAED,AAAOM,KAAK,CAACC,WAA+B,EAAW;QACtD,MAAMT,cAAc,GAAG,IAAI,CAACC,OAAO,CAACC,MAAM;QAE1C,IAAI,CAACD,OAAO,GAAGL,OAAO,CACrB,IAAI,CAACK,OAAO,CAACM,MAAM,CAAC,IAAI,CAACF,gBAAgB,CAAC,EAC1CI,WAAW,CACX;QAED,OAAOT,cAAc,GAAG,CAAC,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,CAAA;KACtD;IAED,AAAOQ,KAAK,GAAS;QACpB,IAAI,CAACT,OAAO,GAAG,EAAE;KACjB;IA7BD,YAAmBI,gBAA+B,CAAE;QAHpD,KAAQJ,OAAO,GAAU,EAAE,AAL5B,CAK4B;QAI1B,IAAI,CAACI,gBAAgB,GAAGA,gBAAgB;KACxC;CA4BD"}