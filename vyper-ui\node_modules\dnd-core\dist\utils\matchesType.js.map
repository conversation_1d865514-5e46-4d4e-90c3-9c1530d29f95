{"version": 3, "sources": ["../../src/utils/matchesType.ts"], "sourcesContent": ["import type { Identifier } from '../interfaces.js'\n\nexport function matchesType(\n\ttargetType: Identifier | Identifier[] | null,\n\tdraggedItemType: Identifier | null,\n): boolean {\n\tif (draggedItemType === null) {\n\t\treturn targetType === null\n\t}\n\treturn Array.isArray(targetType)\n\t\t? (targetType as Identifier[]).some((t) => t === draggedItemType)\n\t\t: targetType === draggedItemType\n}\n"], "names": ["matchesType", "targetType", "draggedItemType", "Array", "isArray", "some", "t"], "mappings": "AAEA,OAAO,SAASA,WAAW,CAC1BC,UAA4C,EAC5CC,eAAkC,EACxB;IACV,IAAIA,eAAe,KAAK,IAAI,EAAE;QAC7B,OAAOD,UAAU,KAAK,IAAI,CAAA;KAC1B;IACD,OAAOE,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,GAC7B,AAACA,UAAU,CAAkBI,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,KAAKJ,eAAe;IAAA,CAAC,GAC/DD,UAAU,KAAKC,eAAe,CAAA;CACjC"}