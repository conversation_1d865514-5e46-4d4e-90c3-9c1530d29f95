{"version": 3, "sources": ["../../src/reducers/dragOffset.ts"], "sourcesContent": ["import {\n\tBEGIN_DRAG,\n\tDROP,\n\t<PERSON>ND_DRAG,\n\tHOVER,\n\tINIT_COORDS,\n} from '../actions/dragDrop/index.js'\nimport type { Action, XYCoord } from '../interfaces.js'\nimport { areCoordsEqual } from '../utils/equality.js'\n\nexport interface State {\n\tinitialSourceClientOffset: XYCoord | null\n\tinitialClientOffset: XYCoord | null\n\tclientOffset: XYCoord | null\n}\n\nconst initialState: State = {\n\tinitialSourceClientOffset: null,\n\tinitialClientOffset: null,\n\tclientOffset: null,\n}\n\nexport function reduce(\n\tstate: State = initialState,\n\taction: Action<{\n\t\tsourceClientOffset: XYCoord\n\t\tclientOffset: XYCoord\n\t}>,\n): State {\n\tconst { payload } = action\n\tswitch (action.type) {\n\t\tcase INIT_COORDS:\n\t\tcase BEGIN_DRAG:\n\t\t\treturn {\n\t\t\t\tinitialSourceClientOffset: payload.sourceClientOffset,\n\t\t\t\tinitialClientOffset: payload.clientOffset,\n\t\t\t\tclientOffset: payload.clientOffset,\n\t\t\t}\n\t\tcase HOVER:\n\t\t\tif (areCoordsEqual(state.clientOffset, payload.clientOffset)) {\n\t\t\t\treturn state\n\t\t\t}\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tclientOffset: payload.clientOffset,\n\t\t\t}\n\t\tcase END_DRAG:\n\t\tcase DROP:\n\t\t\treturn initialState\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n"], "names": ["BEGIN_DRAG", "DROP", "END_DRAG", "HOVER", "INIT_COORDS", "areCoordsEqual", "initialState", "initialSourceClientOffset", "initialClientOffset", "clientOffset", "reduce", "state", "action", "payload", "type", "sourceClientOffset"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SACCA,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,WAAW,QACL,8BAA8B,CAAA;AAErC,SAASC,cAAc,QAAQ,sBAAsB,CAAA;AAQrD,MAAMC,YAAY,GAAU;IAC3BC,yBAAyB,EAAE,IAAI;IAC/BC,mBAAmB,EAAE,IAAI;IACzBC,YAAY,EAAE,IAAI;CAClB;AAED,OAAO,SAASC,MAAM,CACrBC,KAAY,GAAGL,YAAY,EAC3BM,MAGE,EACM;IACR,MAAM,EAAEC,OAAO,CAAA,EAAE,GAAGD,MAAM;IAC1B,OAAQA,MAAM,CAACE,IAAI;QAClB,KAAKV,WAAW,CAAC;QACjB,KAAKJ,UAAU;YACd,OAAO;gBACNO,yBAAyB,EAAEM,OAAO,CAACE,kBAAkB;gBACrDP,mBAAmB,EAAEK,OAAO,CAACJ,YAAY;gBACzCA,YAAY,EAAEI,OAAO,CAACJ,YAAY;aAClC,CAAA;QACF,KAAKN,KAAK;YACT,IAAIE,cAAc,CAACM,KAAK,CAACF,YAAY,EAAEI,OAAO,CAACJ,YAAY,CAAC,EAAE;gBAC7D,OAAOE,KAAK,CAAA;aACZ;YACD,OAAO,kBACHA,KAAK;gBACRF,YAAY,EAAEI,OAAO,CAACJ,YAAY;cAClC,CAAA;QACF,KAAKP,QAAQ,CAAC;QACd,KAAKD,IAAI;YACR,OAAOK,YAAY,CAAA;QACpB;YACC,OAAOK,KAAK,CAAA;KACb;CACD"}