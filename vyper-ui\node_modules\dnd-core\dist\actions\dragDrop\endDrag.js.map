{"version": 3, "sources": ["../../../src/actions/dragDrop/endDrag.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tDragDropManager,\n\tDragDropMonitor,\n\tSentinelAction,\n} from '../../interfaces.js'\nimport { END_DRAG } from './types.js'\n\nexport function createEndDrag(manager: DragDropManager) {\n\treturn function endDrag(): SentinelAction {\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tverifyIsDragging(monitor)\n\n\t\tconst sourceId = monitor.getSourceId()\n\t\tif (sourceId != null) {\n\t\t\tconst source = registry.getSource(sourceId, true)\n\t\t\tsource.endDrag(monitor, sourceId)\n\t\t\tregistry.unpinSource()\n\t\t}\n\t\treturn { type: END_DRAG }\n\t}\n}\n\nfunction verifyIsDragging(monitor: DragDropMonitor) {\n\tinvariant(monitor.isDragging(), 'Cannot call endDrag while not dragging.')\n}\n"], "names": ["invariant", "END_DRAG", "createEndDrag", "manager", "endDrag", "monitor", "getMonitor", "registry", "getRegistry", "verifyIsDragging", "sourceId", "getSourceId", "source", "getSource", "unpinSource", "type", "isDragging"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAOhD,SAASC,QAAQ,QAAQ,YAAY,CAAA;AAErC,OAAO,SAASC,aAAa,CAACC,OAAwB,EAAE;IACvD,OAAO,SAASC,OAAO,GAAmB;QACzC,MAAMC,OAAO,GAAGF,OAAO,CAACG,UAAU,EAAE;QACpC,MAAMC,QAAQ,GAAGJ,OAAO,CAACK,WAAW,EAAE;QACtCC,gBAAgB,CAACJ,OAAO,CAAC;QAEzB,MAAMK,QAAQ,GAAGL,OAAO,CAACM,WAAW,EAAE;QACtC,IAAID,QAAQ,IAAI,IAAI,EAAE;YACrB,MAAME,MAAM,GAAGL,QAAQ,CAACM,SAAS,CAACH,QAAQ,EAAE,IAAI,CAAC;YACjDE,MAAM,CAACR,OAAO,CAACC,OAAO,EAAEK,QAAQ,CAAC;YACj<PERSON>,QAAQ,CAACO,WAAW,EAAE;SACtB;QACD,OAAO;YAAEC,IAAI,EAAEd,QAAQ;SAAE,CAAA;KACzB,CAAA;CACD;AAED,SAASQ,gBAAgB,CAACJ,OAAwB,EAAE;IACnDL,SAAS,CAACK,OAAO,CAACW,UAAU,EAAE,EAAE,yCAAyC,CAAC;CAC1E"}