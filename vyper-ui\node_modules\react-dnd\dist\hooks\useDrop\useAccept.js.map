{"version": 3, "sources": ["../../../src/hooks/useDrop/useAccept.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { Identifier } from 'dnd-core'\nimport { useMemo } from 'react'\n\nimport type { DropTargetHookSpec } from '../types.js'\n\n/**\n * Internal utility hook to get an array-version of spec.accept.\n * The main utility here is that we aren't creating a new array on every render if a non-array spec.accept is passed in.\n * @param spec\n */\nexport function useAccept<O, R, P>(\n\tspec: DropTargetHookSpec<O, R, P>,\n): Identifier[] {\n\tconst { accept } = spec\n\treturn useMemo(() => {\n\t\tinvariant(spec.accept != null, 'accept must be defined')\n\t\treturn Array.isArray(accept) ? accept : [accept]\n\t}, [accept])\n}\n"], "names": ["invariant", "useMemo", "useAccept", "spec", "accept", "Array", "isArray"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAEhD,SAASC,OAAO,QAAQ,OAAO,CAAA;AAI/B;;;;GAIG,CACH,OAAO,SAASC,SAAS,CACxBC,IAAiC,EAClB;IACf,MAAM,EAAEC,MAAM,CAAA,EAAE,GAAGD,IAAI;IACvB,OAAOF,OAAO,CAAC,IAAM;QACpBD,SAAS,CAACG,IAAI,CAACC,MAAM,IAAI,IAAI,EAAE,wBAAwB,CAAC;QACxD,OAAOC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG;YAACA,MAAM;SAAC,CAAA;KAChD,EAAE;QAACA,MAAM;KAAC,CAAC,CAAA;CACZ"}