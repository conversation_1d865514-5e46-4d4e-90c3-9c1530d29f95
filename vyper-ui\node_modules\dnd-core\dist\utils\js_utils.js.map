{"version": 3, "sources": ["../../src/utils/js_utils.ts"], "sourcesContent": ["// cheap lodash replacements\n\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */\nexport function get<T>(obj: any, path: string, defaultValue: T): T {\n\treturn path\n\t\t.split('.')\n\t\t.reduce((a, c) => (a && a[c] ? a[c] : defaultValue || null), obj) as T\n}\n\n/**\n * drop-in replacement for _.without\n */\nexport function without<T>(items: T[], item: T): T[] {\n\treturn items.filter((i) => i !== item)\n}\n\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isString(input: any): boolean {\n\treturn typeof input === 'string'\n}\n\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isObject(input: any): boolean {\n\treturn typeof input === 'object'\n}\n\n/**\n * replacement for _.xor\n * @param itemsA\n * @param itemsB\n */\nexport function xor<T extends string | number>(itemsA: T[], itemsB: T[]): T[] {\n\tconst map = new Map<T, number>()\n\tconst insertItem = (item: T) => {\n\t\tmap.set(item, map.has(item) ? (map.get(item) as number) + 1 : 1)\n\t}\n\titemsA.forEach(insertItem)\n\titemsB.forEach(insertItem)\n\n\tconst result: T[] = []\n\tmap.forEach((count, key) => {\n\t\tif (count === 1) {\n\t\t\tresult.push(key)\n\t\t}\n\t})\n\treturn result\n}\n\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */\nexport function intersection<T>(itemsA: T[], itemsB: T[]): T[] {\n\treturn itemsA.filter((t) => itemsB.indexOf(t) > -1)\n}\n"], "names": ["get", "obj", "path", "defaultValue", "split", "reduce", "a", "c", "without", "items", "item", "filter", "i", "isString", "input", "isObject", "xor", "itemsA", "itemsB", "map", "Map", "insertItem", "set", "has", "for<PERSON>ach", "result", "count", "key", "push", "intersection", "t", "indexOf"], "mappings": "AAAA,4BAA4B;AAE5B;;;;;GAKG,CACH,OAAO,SAASA,GAAG,CAAIC,GAAQ,EAAEC,IAAY,EAAEC,YAAe,EAAK;IAClE,OAAOD,IAAI,CACTE,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAMD,CAAC,IAAIA,CAAC,CAACC,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,GAAGJ,YAAY,IAAI,IAAI;IAAC,EAAEF,GAAG,CAAC,CAAK;CACvE;AAED;;GAEG,CACH,OAAO,SAASO,OAAO,CAAIC,KAAU,EAAEC,IAAO,EAAO;IACpD,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,KAAKF,IAAI;IAAA,CAAC,CAAA;CACtC;AAED;;;GAG<PERSON>,CACH,OAAO,SAASG,QAAQ,CAACC,KAAU,EAAW;IAC7C,OAAO,OAAOA,KAAK,KAAK,QAAQ,CAAA;CAChC;AAED;;;GAGG,CACH,OAAO,SAASC,QAAQ,CAACD,KAAU,EAAW;IAC7C,OAAO,OAAOA,KAAK,KAAK,QAAQ,CAAA;CAChC;AAED;;;;GAIG,CACH,OAAO,SAASE,GAAG,CAA4BC,MAAW,EAAEC,MAAW,EAAO;IAC7E,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAa;IAChC,MAAMC,UAAU,GAAG,CAACX,IAAO,GAAK;QAC/BS,GAAG,CAACG,GAAG,CAACZ,IAAI,EAAES,GAAG,CAACI,GAAG,CAACb,IAAI,CAAC,GAAG,AAACS,GAAG,CAACnB,GAAG,CAACU,IAAI,CAAC,GAAc,CAAC,GAAG,CAAC,CAAC;KAChE;IACDO,MAAM,CAACO,OAAO,CAACH,UAAU,CAAC;IAC1BH,MAAM,CAACM,OAAO,CAACH,UAAU,CAAC;IAE1B,MAAMI,MAAM,GAAQ,EAAE;IACtBN,GAAG,CAACK,OAAO,CAAC,CAACE,KAAK,EAAEC,GAAG,GAAK;QAC3B,IAAID,KAAK,KAAK,CAAC,EAAE;YAChBD,MAAM,CAACG,IAAI,CAACD,GAAG,CAAC;SAChB;KACD,CAAC;IACF,OAAOF,MAAM,CAAA;CACb;AAED;;;;GAIG,CACH,OAAO,SAASI,YAAY,CAAIZ,MAAW,EAAEC,MAAW,EAAO;IAC9D,OAAOD,MAAM,CAACN,MAAM,CAAC,CAACmB,CAAC,GAAKZ,MAAM,CAACa,OAAO,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAA,CAAC,CAAA;CACnD"}