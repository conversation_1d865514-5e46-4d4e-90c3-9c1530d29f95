{"version": 3, "sources": ["../src/getEmptyImage.ts"], "sourcesContent": ["let emptyImage: HTMLImageElement | undefined\n\nexport function getEmptyImage(): HTMLImageElement {\n\tif (!emptyImage) {\n\t\temptyImage = new Image()\n\t\temptyImage.src =\n\t\t\t'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=='\n\t}\n\n\treturn emptyImage\n}\n"], "names": ["emptyImage", "getEmptyImage", "Image", "src"], "mappings": "AAAA,IAAIA,UAAU,AAA8B;AAE5C,OAAO,SAASC,aAAa,GAAqB;IACjD,IAAI,CAACD,UAAU,EAAE;QAChBA,UAAU,GAAG,IAAIE,KAAK,EAAE;QACxBF,UAAU,CAACG,GAAG,GACb,4EAA4E;KAC7E;IAED,OAAOH,UAAU,CAAA;CACjB"}