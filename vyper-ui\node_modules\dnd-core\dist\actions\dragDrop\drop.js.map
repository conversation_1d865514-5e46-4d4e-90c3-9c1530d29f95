{"version": 3, "sources": ["../../../src/actions/dragDrop/drop.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tDragDropManager,\n\tDragDropMonitor,\n\tDropPayload,\n\tHandlerRegistry,\n\tIdentifier,\n} from '../../interfaces.js'\nimport { isObject } from '../../utils/js_utils.js'\nimport { DROP } from './types.js'\n\nexport function createDrop(manager: DragDropManager) {\n\treturn function drop(options = {}): void {\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tverifyInvariants(monitor)\n\t\tconst targetIds = getDroppableTargets(monitor)\n\n\t\t// Multiple actions are dispatched here, which is why this doesn't return an action\n\t\ttargetIds.forEach((targetId, index) => {\n\t\t\tconst dropResult = determineDropResult(targetId, index, registry, monitor)\n\t\t\tconst action: Action<DropPayload> = {\n\t\t\t\ttype: DROP,\n\t\t\t\tpayload: {\n\t\t\t\t\tdropResult: {\n\t\t\t\t\t\t...options,\n\t\t\t\t\t\t...dropResult,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t}\n\t\t\tmanager.dispatch(action)\n\t\t})\n\t}\n}\n\nfunction verifyInvariants(monitor: DragDropMonitor) {\n\tinvariant(monitor.isDragging(), 'Cannot call drop while not dragging.')\n\tinvariant(\n\t\t!monitor.didDrop(),\n\t\t'Cannot call drop twice during one drag operation.',\n\t)\n}\n\nfunction determineDropResult(\n\ttargetId: Identifier,\n\tindex: number,\n\tregistry: HandlerRegistry,\n\tmonitor: DragDropMonitor,\n) {\n\tconst target = registry.getTarget(targetId)\n\tlet dropResult = target ? target.drop(monitor, targetId) : undefined\n\tverifyDropResultType(dropResult)\n\tif (typeof dropResult === 'undefined') {\n\t\tdropResult = index === 0 ? {} : monitor.getDropResult()\n\t}\n\treturn dropResult\n}\n\nfunction verifyDropResultType(dropResult: any) {\n\tinvariant(\n\t\ttypeof dropResult === 'undefined' || isObject(dropResult),\n\t\t'Drop result must either be an object or undefined.',\n\t)\n}\n\nfunction getDroppableTargets(monitor: DragDropMonitor) {\n\tconst targetIds = monitor\n\t\t.getTargetIds()\n\t\t.filter(monitor.canDropOnTarget, monitor)\n\ttargetIds.reverse()\n\treturn targetIds\n}\n"], "names": ["invariant", "isObject", "DROP", "createDrop", "manager", "drop", "options", "monitor", "getMonitor", "registry", "getRegistry", "verifyInvariants", "targetIds", "getDroppableTargets", "for<PERSON>ach", "targetId", "index", "dropResult", "determineDropResult", "action", "type", "payload", "dispatch", "isDragging", "didDrop", "target", "get<PERSON><PERSON><PERSON>", "undefined", "verifyDropResultType", "getDropResult", "getTargetIds", "filter", "canDropOnTarget", "reverse"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAUhD,SAASC,QAAQ,QAAQ,yBAAyB,CAAA;AAClD,SAASC,IAAI,QAAQ,YAAY,CAAA;AAEjC,OAAO,SAASC,UAAU,CAACC,OAAwB,EAAE;IACpD,OAAO,SAASC,IAAI,CAACC,OAAO,GAAG,EAAE,EAAQ;QACxC,MAAMC,OAAO,GAAGH,OAAO,CAACI,UAAU,EAAE;QACpC,MAAMC,QAAQ,GAAGL,OAAO,CAACM,WAAW,EAAE;QACtCC,gBAAgB,CAACJ,OAAO,CAAC;QACzB,MAAMK,SAAS,GAAGC,mBAAmB,CAACN,OAAO,CAAC;QAE9C,mFAAmF;QACnFK,SAAS,CAACE,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,GAAK;YACtC,MAAMC,UAAU,GAAGC,mBAAmB,CAACH,QAAQ,EAAEC,KAAK,EAAEP,QAAQ,EAAEF,OAAO,CAAC;YAC1E,MAAMY,MAAM,GAAwB;gBACnCC,IAAI,EAAElB,IAAI;gBACVmB,OAAO,EAAE;oBACRJ,UAAU,EAAE,kBACRX,OAAO,EACPW,UAAU,CACb;iBACD;aACD;YACDb,OAAO,CAACkB,QAAQ,CAACH,MAAM,CAAC;SACxB,CAAC;KACF,CAAA;CACD;AAED,SAASR,gBAAgB,CAACJ,OAAwB,EAAE;IACnDP,SAAS,CAACO,OAAO,CAACgB,UAAU,EAAE,EAAE,sCAAsC,CAAC;IACvEvB,SAAS,CACR,CAACO,OAAO,CAACiB,OAAO,EAAE,EAClB,mDAAmD,CACnD;CACD;AAED,SAASN,mBAAmB,CAC3BH,QAAoB,EACpBC,KAAa,EACbP,QAAyB,EACzBF,OAAwB,EACvB;IACD,MAAMkB,MAAM,GAAGhB,QAAQ,CAACiB,SAAS,CAACX,QAAQ,CAAC;IAC3C,IAAIE,UAAU,GAAGQ,MAAM,GAAGA,MAAM,CAACpB,IAAI,CAACE,OAAO,EAAEQ,QAAQ,CAAC,GAAGY,SAAS;IACpEC,oBAAoB,CAACX,UAAU,CAAC;IAChC,IAAI,OAAOA,UAAU,KAAK,WAAW,EAAE;QACtCA,UAAU,GAAGD,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGT,OAAO,CAACsB,aAAa,EAAE;KACvD;IACD,OAAOZ,UAAU,CAAA;CACjB;AAED,SAASW,oBAAoB,CAACX,UAAe,EAAE;IAC9CjB,SAAS,CACR,OAAOiB,UAAU,KAAK,WAAW,IAAIhB,QAAQ,CAACgB,UAAU,CAAC,EACzD,oDAAoD,CACpD;CACD;AAED,SAASJ,mBAAmB,CAACN,OAAwB,EAAE;IACtD,MAAMK,SAAS,GAAGL,OAAO,CACvBuB,YAAY,EAAE,CACdC,MAAM,CAACxB,OAAO,CAACyB,eAAe,EAAEzB,OAAO,CAAC;IAC1CK,SAAS,CAACqB,OAAO,EAAE;IACnB,OAAOrB,SAAS,CAAA;CAChB"}