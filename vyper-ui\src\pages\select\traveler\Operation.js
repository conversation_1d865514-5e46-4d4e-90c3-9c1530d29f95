import makeStyles from "@material-ui/core/styles/makeStyles";
import clsx from "clsx";
import React, { useContext } from "react";
import { useDrop, useDrag } from "react-dnd";
import DragIndicatorIcon from "@material-ui/icons/DragIndicator";
import { EngineeringIcon } from "src/component/icons/EngineeringIcon";
import { VyperLink } from "src/pages/mockup/VyperLink";
import { ApprovedText } from "src/pages/select/traveler/ApprovedText";
import { ApprovalOperationContext } from "../../../component/approvaloperation/ApprovalOperation";
import { AuthContext } from "../../../component/common";
import { SourceIcon } from "../../../component/sourceicon/SourceIcon";
import { AddRequiredButton } from "./buttons/AddRequiredButton";
import { OperationButtons } from "./buttons/OperationButtons";
import { Component } from "./Component";
import DuplicatedOperationWarning from "./DuplicatedOperationWarning";
import {
  NON_VALIDATABLE_OPERATIONS,
  ValidatedOperation,
  validateOperation,
} from "./ValidatedOperation";

const useStyles = makeStyles({
  icon: {
    fontSize: "0.75em",
    "&:hover": {
      backgroundColor: "#ccc",
    },
  },
  operationComment: {
    fontSize: "1.5rem",
    backgroundColor: "hsla(0, 100%, 95%, 1)",
    display: "flex",
    justifyContent: "center",
    border: "1px solid red",
  },
  operationText: {
    paddingTop: "0.5em",
    paddingBottom: "0.5em",
  },
  centered: {
    display: "flex",
    whiteSpace: "nowrap",
  },
  deleted: {
    textDecoration: "line-through",
  },
  dragHandle: {
    display: "inline-flex",
    alignItems: "center",
    cursor: "grab",
    color: "#666",
    marginRight: "8px",
    marginLeft: "24px",
    "&:hover": {
      color: "#333",
    },
    "&:active": {
      cursor: "grabbing",
    },
  },
  draggableContainer: {
    display: "flex",
    alignItems: "flex-start",
  },
  componentWrapper: {
    flex: 1,
  },
  draggingComponent: {
    backgroundColor: "#f0f8ff",
    borderRadius: "4px",
    boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
    transition: "background-color 0.2s ease, box-shadow 0.2s ease",
  },
});



export const Operation = ({
  vyper,
  build,
  options,
  operation,
  onEditOperation,
  onAddOperation,
  onRemoveOperation,
  onEditComponentName,
  onEditComponentValue,
  onAddComponent,
  onRemoveComponent,
  validatedOperation,
  onClickValidate,
  onCommentOperation,
  approvedGroups,
  onUndoDelete,
  reworkedTraveler,
  onSelectDiagramApproval,
  unApprovedGroups,
  isDragDropEnabled,
  onComponentReorder,
}) => {
  const { authUser } = useContext(AuthContext);

  const { findApprovalOperationByOperation } = useContext(
    ApprovalOperationContext
  );

  const vo = validateOperation({
    buildState: build.state,
    buildFacility: build.facility.object.PDBFacility,
    sbe: build.material.object.SBE,
    sbe1: build.material.object.SBE1,
    operation: operation,
    authUser,
    validatedOperation,
    findApprovalOperationByOperation: findApprovalOperationByOperation,
    operations: build.traveler.operations,
    unApprovedGroups,
  });

  const classes = useStyles();

  // Move component function for drag and drop
  const moveComponent = (dragIndex, hoverIndex, sourceOperationName, destOperationName) => {
    // Create a result object similar to react-beautiful-dnd
    const result = {
      source: {
        index: dragIndex,
        droppableId: `components-${sourceOperationName}`
      },
      destination: {
        index: hoverIndex,
        droppableId: `components-${destOperationName}`
      }
    };

    onComponentReorder(result);
  };

  // DraggableComponent component for react-dnd (only used when drag-drop is enabled)
  const DraggableComponent = ({
    component,
    index,
    operationName,
    isDragDropEnabled,
    priority
  }) => {
    const [{ isDragging }, drag] = useDrag({
      type: 'component',
      item: {
        index,
        component,
        operationName,
        sourceOperationName: operationName
      },
      canDrag: isDragDropEnabled,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    const [, drop] = useDrop({
      accept: 'component',
      hover: (draggedItem) => {
        if (draggedItem.operationName === operationName && draggedItem.index !== index) {
          moveComponent(draggedItem.index, index, operationName, operationName);
          draggedItem.index = index;
        }
      },
      drop: (draggedItem) => {
        if (draggedItem.operationName !== operationName) {
          // Cross-operation move
          moveComponent(draggedItem.index, index, draggedItem.sourceOperationName, operationName);
        }
      }
    });

    return (
      <div ref={(node) => drag(drop(node))} style={{ opacity: isDragging ? 0.5 : 1 }}>
        <div className={clsx(
          classes.draggableContainer,
          isDragging && classes.draggingComponent
        )}>
          {isDragDropEnabled && (
            <div className={classes.dragHandle}>
              <DragIndicatorIcon fontSize="small" />
            </div>
          )}
          <div className={classes.componentWrapper}>
            <Component
              vyper={vyper}
              build={build}
              disabled={disabled}
              operation={operation}
              component={component}
              options={options}
              onEditName={(o, c) => onEditComponentName(o, c, index)}
              onEditValue={(o, c) => onEditComponentValue(o, c, index)}
              onAdd={(o, c) => onAddComponent(o, c, index)}
              onRemove={(o, c) => onRemoveComponent(o, c, index)}
              priority={priority}
              reworkedTraveler={reworkedTraveler}
              onSelectDiagramApproval={onSelectDiagramApproval}
              checkDisableComponent={checkDisableComponent()}
            />
          </div>
        </div>
      </div>
    );
  };

  // Regular component wrapper for non-drag-drop mode
  const RegularComponent = ({
    component,
    index,
    priority
  }) => {
    return (
      <Component
        vyper={vyper}
        build={build}
        disabled={disabled}
        operation={operation}
        component={component}
        options={options}
        onEditName={(o, c) => onEditComponentName(o, c, index)}
        onEditValue={(o, c) => onEditComponentValue(o, c, index)}
        onAdd={(o, c) => onAddComponent(o, c, index)}
        onRemove={(o, c) => onRemoveComponent(o, c, index)}
        priority={priority}
        reworkedTraveler={reworkedTraveler}
        onSelectDiagramApproval={onSelectDiagramApproval}
        checkDisableComponent={checkDisableComponent()}
      />
    );
  };

  // disable changes when the operation has been validated
  const disabled = vo.checked;

  // disable component edit changes depending on build state and owner
  const disableComponentEdit = !vo.enabled || disabled;
  const checkDisableComponent = () => {
    // Check the build state
    let buildState = build.state;

    switch (buildState) {
      case "AT_REVIEW_CHANGE":
        return disableComponentEdit;

      default:
        return disabled;
    }
  };

  const ao = findApprovalOperationByOperation(operation.name);

  // if this operation has been approved, get the approvingGroup object
  // which is { group:"", username:"", date:""}
  const approvedGroup = approvedGroups.find((group) =>
    group.group.toLowerCase().includes(ao?.groupText?.toLowerCase())
  );

  // used to track the component, and determine the priority
  const priorityMap = {};

  const showApproval = !NON_VALIDATABLE_OPERATIONS.includes(operation?.name);

  return (
    <div>
      <br />

      <OperationButtons
        disabled={disabled}
        options={options}
        operation={operation}
        onAdd={onAddOperation}
        onEdit={onEditOperation}
        onRemove={onRemoveOperation}
        onComment={onCommentOperation}
        vyper={vyper}
        build={build}
        vo={vo}
      />

      <SourceIcon
        disabled={operation.engineeringDeleted}
        source={operation.source}
        heading="The operation was added by"
      />

      <ValidatedOperation
        vo={vo}
        operation={operation}
        onClickValidate={onClickValidate}
      >
        <span
          className={clsx({ [classes.deleted]: operation.engineeringDeleted })}
        >
          <VyperLink
            canEdit={operation.engineeringDeleted}
            onClick={() => onUndoDelete(operation)}
          >
            {operation.name}
          </VyperLink>
        </span>
        {operation.engineering === "Y" ? <EngineeringIcon /> : null}
      </ValidatedOperation>

      {vo.isDuplicated && <DuplicatedOperationWarning />}

      {showApproval && <ApprovedText approvedGroup={approvedGroup} />}

      <br />

      {operation.comment == null ? null : (
        <div className={classes.operationComment}>
          <span className={classes.operationText}>{operation.comment}</span>
        </div>
      )}

      {options.editbutton && options.component ? (
        <div>
          &nbsp;&nbsp;&nbsp;
          <AddRequiredButton
            title="Add Component"
            disabled={disabled}
            onClick={() => onAddComponent(operation, null)}
          />
        </div>
      ) : null}

      {options.component && (
        <div>
          {operation.components.map((component, n) => {
            let priority;
            if (component.name in priorityMap) {
              priorityMap[component.name]++;
              priority = priorityMap[component.name];
            } else {
              priorityMap[component.name] = 1;
              priority = 1;
            }

            return isDragDropEnabled ? (
              <DraggableComponent
                key={`${operation.name}-${component.name}-${n}`}
                component={component}
                index={n}
                operationName={operation.name}
                isDragDropEnabled={isDragDropEnabled}
                priority={priority}
              />
            ) : (
              <RegularComponent
                key={`${operation.name}-${component.name}-${n}`}
                component={component}
                index={n}
                priority={priority}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};