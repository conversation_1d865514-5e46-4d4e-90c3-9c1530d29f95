{"version": 3, "sources": ["../../../src/hooks/useDrag/useRegisteredDragSource.ts"], "sourcesContent": ["import type { SourceConnector } from '../../internals/index.js'\nimport { registerSource } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport type { DragSourceHookSpec } from '../types.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\nimport { useDragSource } from './useDragSource.js'\nimport { useDragType } from './useDragType.js'\n\nexport function useRegisteredDragSource<O, R, P>(\n\tspec: DragSourceHookSpec<O, R, P>,\n\tmonitor: DragSourceMonitor<O, R>,\n\tconnector: SourceConnector,\n): void {\n\tconst manager = useDragDropManager()\n\tconst handler = useDragSource(spec, monitor, connector)\n\tconst itemType = useDragType(spec)\n\n\tuseIsomorphicLayoutEffect(\n\t\tfunction registerDragSource() {\n\t\t\tif (itemType != null) {\n\t\t\t\tconst [handlerId, unregister] = registerSource(\n\t\t\t\t\titemType,\n\t\t\t\t\thandler,\n\t\t\t\t\tmanager,\n\t\t\t\t)\n\t\t\t\tmonitor.receiveHandlerId(handlerId)\n\t\t\t\tconnector.receiveHandlerId(handlerId)\n\t\t\t\treturn unregister\n\t\t\t}\n\t\t\treturn\n\t\t},\n\t\t[manager, monitor, connector, handler, itemType],\n\t)\n}\n"], "names": ["registerSource", "useDragDropManager", "useIsomorphicLayoutEffect", "useDragSource", "useDragType", "useRegisteredDragSource", "spec", "monitor", "connector", "manager", "handler", "itemType", "registerDragSource", "handlerId", "unregister", "receiveHandlerId"], "mappings": "AACA,SAASA,cAAc,QAAQ,0BAA0B,CAAA;AAGzD,SAASC,kBAAkB,QAAQ,0BAA0B,CAAA;AAC7D,SAASC,yBAAyB,QAAQ,iCAAiC,CAAA;AAC3E,SAASC,aAAa,QAAQ,oBAAoB,CAAA;AAClD,SAASC,WAAW,QAAQ,kBAAkB,CAAA;AAE9C,OAAO,SAASC,uBAAuB,CACtCC,IAAiC,EACjCC,OAAgC,EAChCC,SAA0B,EACnB;IACP,MAAMC,OAAO,GAAGR,kBAAkB,EAAE;IACpC,MAAMS,OAAO,GAAGP,aAAa,CAACG,IAAI,EAAEC,OAAO,EAAEC,SAAS,CAAC;IACvD,MAAMG,QAAQ,GAAGP,WAAW,CAACE,IAAI,CAAC;IAElCJ,yBAAyB,CACxB,SAASU,kBAAkB,GAAG;QAC7B,IAAID,QAAQ,IAAI,IAAI,EAAE;YACrB,MAAM,CAACE,SAAS,EAAEC,UAAU,CAAC,GAAGd,cAAc,CAC7CW,QAAQ,EACRD,OAAO,EACPD,OAAO,CACP;YACDF,OAAO,CAACQ,gBAAgB,CAACF,SAAS,CAAC;YACnCL,SAAS,CAACO,gBAAgB,CAACF,SAAS,CAAC;YACrC,OAAOC,UAAU,CAAA;SACjB;QACD,OAAM;KACN,EACD;QAACL,OAAO;QAAEF,OAAO;QAAEC,SAAS;QAAEE,OAAO;QAAEC,QAAQ;KAAC,CAChD;CACD"}