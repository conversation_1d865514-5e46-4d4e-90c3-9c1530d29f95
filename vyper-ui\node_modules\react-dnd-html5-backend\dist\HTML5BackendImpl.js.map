{"version": 3, "sources": ["../src/HTML5BackendImpl.ts"], "sourcesContent": ["import type {\n\tBackend,\n\t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport { EnterLeaveCounter } from './EnterLeaveCounter.js'\nimport {\n\tcreateNativeDragSource,\n\tmatchNativeItemType,\n} from './NativeDragSources/index.js'\nimport type { NativeDragSource } from './NativeDragSources/NativeDragSource.js'\nimport * as NativeTypes from './NativeTypes.js'\nimport {\n\tgetDragPreviewOffset,\n\tgetEventClientOffset,\n\tgetNodeClientOffset,\n} from './OffsetUtils.js'\nimport { OptionsReader } from './OptionsReader.js'\nimport type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\n\ntype RootNode = Node & { __isReactDndBackendSetUp: boolean | undefined }\n\nexport class HTML5BackendImpl implements Backend {\n\tprivate options: OptionsReader\n\n\t// React-Dnd Components\n\tprivate actions: DragDropActions\n\tprivate monitor: DragDropMonitor\n\tprivate registry: HandlerRegistry\n\n\t// Internal State\n\tprivate enterLeaveCounter: EnterLeaveCounter\n\n\tprivate sourcePreviewNodes: Map<string, Element> = new Map()\n\tprivate sourcePreviewNodeOptions: Map<string, any> = new Map()\n\tprivate sourceNodes: Map<string, Element> = new Map()\n\tprivate sourceNodeOptions: Map<string, any> = new Map()\n\n\tprivate dragStartSourceIds: string[] | null = null\n\tprivate dropTargetIds: string[] = []\n\tprivate dragEnterTargetIds: string[] = []\n\tprivate currentNativeSource: NativeDragSource | null = null\n\tprivate currentNativeHandle: Identifier | null = null\n\tprivate currentDragSourceNode: Element | null = null\n\tprivate altKeyPressed = false\n\tprivate mouseMoveTimeoutTimer: number | null = null\n\tprivate asyncEndDragFrameId: number | null = null\n\tprivate dragOverTargetIds: string[] | null = null\n\n\tprivate lastClientOffset: XYCoord | null = null\n\tprivate hoverRafId: number | null = null\n\n\tpublic constructor(\n\t\tmanager: DragDropManager,\n\t\tglobalContext?: HTML5BackendContext,\n\t\toptions?: HTML5BackendOptions,\n\t) {\n\t\tthis.options = new OptionsReader(globalContext, options)\n\t\tthis.actions = manager.getActions()\n\t\tthis.monitor = manager.getMonitor()\n\t\tthis.registry = manager.getRegistry()\n\t\tthis.enterLeaveCounter = new EnterLeaveCounter(this.isNodeInDocument)\n\t}\n\n\t/**\n\t * Generate profiling statistics for the HTML5Backend.\n\t */\n\tpublic profile(): Record<string, number> {\n\t\treturn {\n\t\t\tsourcePreviewNodes: this.sourcePreviewNodes.size,\n\t\t\tsourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n\t\t\tsourceNodeOptions: this.sourceNodeOptions.size,\n\t\t\tsourceNodes: this.sourceNodes.size,\n\t\t\tdragStartSourceIds: this.dragStartSourceIds?.length || 0,\n\t\t\tdropTargetIds: this.dropTargetIds.length,\n\t\t\tdragEnterTargetIds: this.dragEnterTargetIds.length,\n\t\t\tdragOverTargetIds: this.dragOverTargetIds?.length || 0,\n\t\t}\n\t}\n\n\t// public for test\n\tpublic get window(): Window | undefined {\n\t\treturn this.options.window\n\t}\n\tpublic get document(): Document | undefined {\n\t\treturn this.options.document\n\t}\n\t/**\n\t * Get the root element to use for event subscriptions\n\t */\n\tprivate get rootElement(): Node | undefined {\n\t\treturn this.options.rootElement as Node\n\t}\n\n\tpublic setup(): void {\n\t\tconst root = this.rootElement as RootNode | undefined\n\t\tif (root === undefined) {\n\t\t\treturn\n\t\t}\n\n\t\tif (root.__isReactDndBackendSetUp) {\n\t\t\tthrow new Error('Cannot have two HTML5 backends at the same time.')\n\t\t}\n\t\troot.__isReactDndBackendSetUp = true\n\t\tthis.addEventListeners(root)\n\t}\n\n\tpublic teardown(): void {\n\t\tconst root = this.rootElement as RootNode\n\t\tif (root === undefined) {\n\t\t\treturn\n\t\t}\n\n\t\troot.__isReactDndBackendSetUp = false\n\t\tthis.removeEventListeners(this.rootElement as Element)\n\t\tthis.clearCurrentDragSourceNode()\n\t\tif (this.asyncEndDragFrameId) {\n\t\t\tthis.window?.cancelAnimationFrame(this.asyncEndDragFrameId)\n\t\t}\n\t}\n\n\tpublic connectDragPreview(\n\t\tsourceId: string,\n\t\tnode: Element,\n\t\toptions: any,\n\t): Unsubscribe {\n\t\tthis.sourcePreviewNodeOptions.set(sourceId, options)\n\t\tthis.sourcePreviewNodes.set(sourceId, node)\n\n\t\treturn (): void => {\n\t\t\tthis.sourcePreviewNodes.delete(sourceId)\n\t\t\tthis.sourcePreviewNodeOptions.delete(sourceId)\n\t\t}\n\t}\n\n\tpublic connectDragSource(\n\t\tsourceId: string,\n\t\tnode: Element,\n\t\toptions: any,\n\t): Unsubscribe {\n\t\tthis.sourceNodes.set(sourceId, node)\n\t\tthis.sourceNodeOptions.set(sourceId, options)\n\n\t\tconst handleDragStart = (e: any) => this.handleDragStart(e, sourceId)\n\t\tconst handleSelectStart = (e: any) => this.handleSelectStart(e)\n\n\t\tnode.setAttribute('draggable', 'true')\n\t\tnode.addEventListener('dragstart', handleDragStart)\n\t\tnode.addEventListener('selectstart', handleSelectStart)\n\n\t\treturn (): void => {\n\t\t\tthis.sourceNodes.delete(sourceId)\n\t\t\tthis.sourceNodeOptions.delete(sourceId)\n\n\t\t\tnode.removeEventListener('dragstart', handleDragStart)\n\t\t\tnode.removeEventListener('selectstart', handleSelectStart)\n\t\t\tnode.setAttribute('draggable', 'false')\n\t\t}\n\t}\n\n\tpublic connectDropTarget(targetId: string, node: HTMLElement): Unsubscribe {\n\t\tconst handleDragEnter = (e: DragEvent) => this.handleDragEnter(e, targetId)\n\t\tconst handleDragOver = (e: DragEvent) => this.handleDragOver(e, targetId)\n\t\tconst handleDrop = (e: DragEvent) => this.handleDrop(e, targetId)\n\n\t\tnode.addEventListener('dragenter', handleDragEnter)\n\t\tnode.addEventListener('dragover', handleDragOver)\n\t\tnode.addEventListener('drop', handleDrop)\n\n\t\treturn (): void => {\n\t\t\tnode.removeEventListener('dragenter', handleDragEnter)\n\t\t\tnode.removeEventListener('dragover', handleDragOver)\n\t\t\tnode.removeEventListener('drop', handleDrop)\n\t\t}\n\t}\n\n\tprivate addEventListeners(target: Node) {\n\t\t// SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n\t\tif (!target.addEventListener) {\n\t\t\treturn\n\t\t}\n\t\ttarget.addEventListener(\n\t\t\t'dragstart',\n\t\t\tthis.handleTopDragStart as EventListener,\n\t\t)\n\t\ttarget.addEventListener('dragstart', this.handleTopDragStartCapture, true)\n\t\ttarget.addEventListener('dragend', this.handleTopDragEndCapture, true)\n\t\ttarget.addEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnter as EventListener,\n\t\t)\n\t\ttarget.addEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnterCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.addEventListener(\n\t\t\t'dragleave',\n\t\t\tthis.handleTopDragLeaveCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.addEventListener('dragover', this.handleTopDragOver as EventListener)\n\t\ttarget.addEventListener(\n\t\t\t'dragover',\n\t\t\tthis.handleTopDragOverCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.addEventListener('drop', this.handleTopDrop as EventListener)\n\t\ttarget.addEventListener(\n\t\t\t'drop',\n\t\t\tthis.handleTopDropCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t}\n\n\tprivate removeEventListeners(target: Node) {\n\t\t// SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n\t\tif (!target.removeEventListener) {\n\t\t\treturn\n\t\t}\n\t\ttarget.removeEventListener('dragstart', this.handleTopDragStart as any)\n\t\ttarget.removeEventListener(\n\t\t\t'dragstart',\n\t\t\tthis.handleTopDragStartCapture,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener('dragend', this.handleTopDragEndCapture, true)\n\t\ttarget.removeEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnter as EventListener,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragenter',\n\t\t\tthis.handleTopDragEnterCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragleave',\n\t\t\tthis.handleTopDragLeaveCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragover',\n\t\t\tthis.handleTopDragOver as EventListener,\n\t\t)\n\t\ttarget.removeEventListener(\n\t\t\t'dragover',\n\t\t\tthis.handleTopDragOverCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t\ttarget.removeEventListener('drop', this.handleTopDrop as EventListener)\n\t\ttarget.removeEventListener(\n\t\t\t'drop',\n\t\t\tthis.handleTopDropCapture as EventListener,\n\t\t\ttrue,\n\t\t)\n\t}\n\n\tprivate getCurrentSourceNodeOptions() {\n\t\tconst sourceId = this.monitor.getSourceId() as string\n\t\tconst sourceNodeOptions = this.sourceNodeOptions.get(sourceId)\n\n\t\treturn {\n\t\t\tdropEffect: this.altKeyPressed ? 'copy' : 'move',\n\t\t\t...(sourceNodeOptions || {}),\n\t\t}\n\t}\n\n\tprivate getCurrentDropEffect() {\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\t// It makes more sense to default to 'copy' for native resources\n\t\t\treturn 'copy'\n\t\t}\n\n\t\treturn this.getCurrentSourceNodeOptions().dropEffect\n\t}\n\n\tprivate getCurrentSourcePreviewNodeOptions() {\n\t\tconst sourceId = this.monitor.getSourceId() as string\n\t\tconst sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId)\n\n\t\treturn {\n\t\t\tanchorX: 0.5,\n\t\t\tanchorY: 0.5,\n\t\t\tcaptureDraggingState: false,\n\t\t\t...(sourcePreviewNodeOptions || {}),\n\t\t}\n\t}\n\n\tprivate getSourceClientOffset = (sourceId: string): XYCoord | null => {\n\t\tconst source = this.sourceNodes.get(sourceId)\n\t\treturn (source && getNodeClientOffset(source as HTMLElement)) || null\n\t}\n\n\tprivate isDraggingNativeItem() {\n\t\tconst itemType = this.monitor.getItemType()\n\t\treturn Object.keys(NativeTypes).some(\n\t\t\t(key: string) => (NativeTypes as any)[key] === itemType,\n\t\t)\n\t}\n\n\tprivate beginDragNativeItem(type: string, dataTransfer?: DataTransfer) {\n\t\tthis.clearCurrentDragSourceNode()\n\n\t\tthis.currentNativeSource = createNativeDragSource(type, dataTransfer)\n\t\tthis.currentNativeHandle = this.registry.addSource(\n\t\t\ttype,\n\t\t\tthis.currentNativeSource,\n\t\t)\n\t\tthis.actions.beginDrag([this.currentNativeHandle])\n\t}\n\n\tprivate endDragNativeItem = (): void => {\n\t\tif (!this.isDraggingNativeItem()) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.actions.endDrag()\n\t\tif (this.currentNativeHandle) {\n\t\t\tthis.registry.removeSource(this.currentNativeHandle)\n\t\t}\n\t\tthis.currentNativeHandle = null\n\t\tthis.currentNativeSource = null\n\t}\n\n\tprivate isNodeInDocument = (node: Node | null | undefined): boolean => {\n\t\t// Check the node either in the main document or in the current context\n\t\treturn Boolean(\n\t\t\tnode &&\n\t\t\t\tthis.document &&\n\t\t\t\tthis.document.body &&\n\t\t\t\tthis.document.body.contains(node),\n\t\t)\n\t}\n\n\tprivate endDragIfSourceWasRemovedFromDOM = (): void => {\n\t\tconst node = this.currentDragSourceNode\n\t\tif (node == null || this.isNodeInDocument(node)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tprivate setCurrentDragSourceNode(node: Element | null) {\n\t\tthis.clearCurrentDragSourceNode()\n\t\tthis.currentDragSourceNode = node\n\n\t\t// A timeout of > 0 is necessary to resolve Firefox issue referenced\n\t\t// See:\n\t\t//   * https://github.com/react-dnd/react-dnd/pull/928\n\t\t//   * https://github.com/react-dnd/react-dnd/issues/869\n\t\tconst MOUSE_MOVE_TIMEOUT = 1000\n\n\t\t// Receiving a mouse event in the middle of a dragging operation\n\t\t// means it has ended and the drag source node disappeared from DOM,\n\t\t// so the browser didn't dispatch the dragend event.\n\t\t//\n\t\t// We need to wait before we start listening for mousemove events.\n\t\t// This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n\t\t// immediately in some browsers.\n\t\t//\n\t\t// See:\n\t\t//   * https://github.com/react-dnd/react-dnd/pull/928\n\t\t//   * https://github.com/react-dnd/react-dnd/issues/869\n\t\t//\n\t\tthis.mouseMoveTimeoutTimer = setTimeout(() => {\n\t\t\treturn this.rootElement?.addEventListener(\n\t\t\t\t'mousemove',\n\t\t\t\tthis.endDragIfSourceWasRemovedFromDOM,\n\t\t\t\ttrue,\n\t\t\t)\n\t\t}, MOUSE_MOVE_TIMEOUT) as any as number\n\t}\n\n\tprivate clearCurrentDragSourceNode() {\n\t\tif (this.currentDragSourceNode) {\n\t\t\tthis.currentDragSourceNode = null\n\n\t\t\tif (this.rootElement) {\n\t\t\t\tthis.window?.clearTimeout(this.mouseMoveTimeoutTimer || undefined)\n\t\t\t\tthis.rootElement.removeEventListener(\n\t\t\t\t\t'mousemove',\n\t\t\t\t\tthis.endDragIfSourceWasRemovedFromDOM,\n\t\t\t\t\ttrue,\n\t\t\t\t)\n\t\t\t}\n\n\t\t\tthis.mouseMoveTimeoutTimer = null\n\t\t\treturn true\n\t\t}\n\n\t\treturn false\n\t}\n\n\tprivate scheduleHover = (dragOverTargetIds: string[] | null) => {\n\t\tif (\n\t\t\tthis.hoverRafId === null &&\n\t\t\ttypeof requestAnimationFrame !== 'undefined'\n\t\t) {\n\t\t\tthis.hoverRafId = requestAnimationFrame(() => {\n\t\t\t\tif (this.monitor.isDragging()) {\n\t\t\t\t\tthis.actions.hover(dragOverTargetIds || [], {\n\t\t\t\t\t\tclientOffset: this.lastClientOffset,\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\tthis.hoverRafId = null\n\t\t\t})\n\t\t}\n\t}\n\n\tprivate cancelHover = () => {\n\t\tif (\n\t\t\tthis.hoverRafId !== null &&\n\t\t\ttypeof cancelAnimationFrame !== 'undefined'\n\t\t) {\n\t\t\tcancelAnimationFrame(this.hoverRafId)\n\t\t\tthis.hoverRafId = null\n\t\t}\n\t}\n\n\tpublic handleTopDragStartCapture = (): void => {\n\t\tthis.clearCurrentDragSourceNode()\n\t\tthis.dragStartSourceIds = []\n\t}\n\n\tpublic handleDragStart(e: DragEvent, sourceId: string): void {\n\t\tif (e.defaultPrevented) {\n\t\t\treturn\n\t\t}\n\n\t\tif (!this.dragStartSourceIds) {\n\t\t\tthis.dragStartSourceIds = []\n\t\t}\n\t\tthis.dragStartSourceIds.unshift(sourceId)\n\t}\n\n\tpublic handleTopDragStart = (e: DragEvent): void => {\n\t\tif (e.defaultPrevented) {\n\t\t\treturn\n\t\t}\n\n\t\tconst { dragStartSourceIds } = this\n\t\tthis.dragStartSourceIds = null\n\n\t\tconst clientOffset = getEventClientOffset(e)\n\n\t\t// Avoid crashing if we missed a drop event or our previous drag died\n\t\tif (this.monitor.isDragging()) {\n\t\t\tthis.actions.endDrag()\n\t\t\tthis.cancelHover()\n\t\t}\n\n\t\t// Don't publish the source just yet (see why below)\n\t\tthis.actions.beginDrag(dragStartSourceIds || [], {\n\t\t\tpublishSource: false,\n\t\t\tgetSourceClientOffset: this.getSourceClientOffset,\n\t\t\tclientOffset,\n\t\t})\n\n\t\tconst { dataTransfer } = e\n\t\tconst nativeType = matchNativeItemType(dataTransfer)\n\n\t\tif (this.monitor.isDragging()) {\n\t\t\tif (dataTransfer && typeof dataTransfer.setDragImage === 'function') {\n\t\t\t\t// Use custom drag image if user specifies it.\n\t\t\t\t// If child drag source refuses drag but parent agrees,\n\t\t\t\t// use parent's node as drag image. Neither works in IE though.\n\t\t\t\tconst sourceId: string = this.monitor.getSourceId() as string\n\t\t\t\tconst sourceNode = this.sourceNodes.get(sourceId)\n\t\t\t\tconst dragPreview = this.sourcePreviewNodes.get(sourceId) || sourceNode\n\n\t\t\t\tif (dragPreview) {\n\t\t\t\t\tconst { anchorX, anchorY, offsetX, offsetY } =\n\t\t\t\t\t\tthis.getCurrentSourcePreviewNodeOptions()\n\t\t\t\t\tconst anchorPoint = { anchorX, anchorY }\n\t\t\t\t\tconst offsetPoint = { offsetX, offsetY }\n\t\t\t\t\tconst dragPreviewOffset = getDragPreviewOffset(\n\t\t\t\t\t\tsourceNode as HTMLElement,\n\t\t\t\t\t\tdragPreview as HTMLElement,\n\t\t\t\t\t\tclientOffset,\n\t\t\t\t\t\tanchorPoint,\n\t\t\t\t\t\toffsetPoint,\n\t\t\t\t\t)\n\n\t\t\t\t\tdataTransfer.setDragImage(\n\t\t\t\t\t\tdragPreview,\n\t\t\t\t\t\tdragPreviewOffset.x,\n\t\t\t\t\t\tdragPreviewOffset.y,\n\t\t\t\t\t)\n\t\t\t\t}\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t// Firefox won't drag without setting data\n\t\t\t\tdataTransfer?.setData('application/json', {} as any)\n\t\t\t} catch (err) {\n\t\t\t\t// IE doesn't support MIME types in setData\n\t\t\t}\n\n\t\t\t// Store drag source node so we can check whether\n\t\t\t// it is removed from DOM and trigger endDrag manually.\n\t\t\tthis.setCurrentDragSourceNode(e.target as Element)\n\n\t\t\t// Now we are ready to publish the drag source.. or are we not?\n\t\t\tconst { captureDraggingState } = this.getCurrentSourcePreviewNodeOptions()\n\t\t\tif (!captureDraggingState) {\n\t\t\t\t// Usually we want to publish it in the next tick so that browser\n\t\t\t\t// is able to screenshot the current (not yet dragging) state.\n\t\t\t\t//\n\t\t\t\t// It also neatly avoids a situation where render() returns null\n\t\t\t\t// in the same tick for the source element, and browser freaks out.\n\t\t\t\tsetTimeout(() => this.actions.publishDragSource(), 0)\n\t\t\t} else {\n\t\t\t\t// In some cases the user may want to override this behavior, e.g.\n\t\t\t\t// to work around IE not supporting custom drag previews.\n\t\t\t\t//\n\t\t\t\t// When using a custom drag layer, the only way to prevent\n\t\t\t\t// the default drag preview from drawing in IE is to screenshot\n\t\t\t\t// the dragging state in which the node itself has zero opacity\n\t\t\t\t// and height. In this case, though, returning null from render()\n\t\t\t\t// will abruptly end the dragging, which is not obvious.\n\t\t\t\t//\n\t\t\t\t// This is the reason such behavior is strictly opt-in.\n\t\t\t\tthis.actions.publishDragSource()\n\t\t\t}\n\t\t} else if (nativeType) {\n\t\t\t// A native item (such as URL) dragged from inside the document\n\t\t\tthis.beginDragNativeItem(nativeType)\n\t\t} else if (\n\t\t\tdataTransfer &&\n\t\t\t!dataTransfer.types &&\n\t\t\t((e.target && !(e.target as Element).hasAttribute) ||\n\t\t\t\t!(e.target as Element).hasAttribute('draggable'))\n\t\t) {\n\t\t\t// Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n\t\t\t// Just let it drag. It's a native type (URL or text) and will be picked up in\n\t\t\t// dragenter handler.\n\t\t\treturn\n\t\t} else {\n\t\t\t// If by this time no drag source reacted, tell browser not to drag.\n\t\t\te.preventDefault()\n\t\t}\n\t}\n\n\tpublic handleTopDragEndCapture = (): void => {\n\t\tif (this.clearCurrentDragSourceNode() && this.monitor.isDragging()) {\n\t\t\t// Firefox can dispatch this event in an infinite loop\n\t\t\t// if dragend handler does something like showing an alert.\n\t\t\t// Only proceed if we have not handled it already.\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tpublic handleTopDragEnterCapture = (e: DragEvent): void => {\n\t\tthis.dragEnterTargetIds = []\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tthis.currentNativeSource?.loadDataTransfer(e.dataTransfer)\n\t\t}\n\n\t\tconst isFirstEnter = this.enterLeaveCounter.enter(e.target)\n\t\tif (!isFirstEnter || this.monitor.isDragging()) {\n\t\t\treturn\n\t\t}\n\n\t\tconst { dataTransfer } = e\n\t\tconst nativeType = matchNativeItemType(dataTransfer)\n\n\t\tif (nativeType) {\n\t\t\t// A native item (such as file or URL) dragged from outside the document\n\t\t\tthis.beginDragNativeItem(nativeType, dataTransfer as DataTransfer)\n\t\t}\n\t}\n\n\tpublic handleDragEnter(_e: DragEvent, targetId: string): void {\n\t\tthis.dragEnterTargetIds.unshift(targetId)\n\t}\n\n\tpublic handleTopDragEnter = (e: DragEvent): void => {\n\t\tconst { dragEnterTargetIds } = this\n\t\tthis.dragEnterTargetIds = []\n\n\t\tif (!this.monitor.isDragging()) {\n\t\t\t// This is probably a native item type we don't understand.\n\t\t\treturn\n\t\t}\n\n\t\tthis.altKeyPressed = e.altKey\n\n\t\t// If the target changes position as the result of `dragenter`, `dragover` might still\n\t\t// get dispatched despite target being no longer there. The easy solution is to check\n\t\t// whether there actually is a target before firing `hover`.\n\t\tif (dragEnterTargetIds.length > 0) {\n\t\t\tthis.actions.hover(dragEnterTargetIds, {\n\t\t\t\tclientOffset: getEventClientOffset(e),\n\t\t\t})\n\t\t}\n\n\t\tconst canDrop = dragEnterTargetIds.some((targetId) =>\n\t\t\tthis.monitor.canDropOnTarget(targetId),\n\t\t)\n\n\t\tif (canDrop) {\n\t\t\t// IE requires this to fire dragover events\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = this.getCurrentDropEffect()\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic handleTopDragOverCapture = (e: DragEvent): void => {\n\t\tthis.dragOverTargetIds = []\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tthis.currentNativeSource?.loadDataTransfer(e.dataTransfer)\n\t\t}\n\t}\n\n\tpublic handleDragOver(_e: DragEvent, targetId: string): void {\n\t\tif (this.dragOverTargetIds === null) {\n\t\t\tthis.dragOverTargetIds = []\n\t\t}\n\t\tthis.dragOverTargetIds.unshift(targetId)\n\t}\n\n\tpublic handleTopDragOver = (e: DragEvent): void => {\n\t\tconst { dragOverTargetIds } = this\n\t\tthis.dragOverTargetIds = []\n\n\t\tif (!this.monitor.isDragging()) {\n\t\t\t// This is probably a native item type we don't understand.\n\t\t\t// Prevent default \"drop and blow away the whole document\" action.\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = 'none'\n\t\t\t}\n\t\t\treturn\n\t\t}\n\n\t\tthis.altKeyPressed = e.altKey\n\t\tthis.lastClientOffset = getEventClientOffset(e)\n\n\t\tthis.scheduleHover(dragOverTargetIds)\n\n\t\tconst canDrop = (dragOverTargetIds || []).some((targetId) =>\n\t\t\tthis.monitor.canDropOnTarget(targetId),\n\t\t)\n\n\t\tif (canDrop) {\n\t\t\t// Show user-specified drop effect.\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = this.getCurrentDropEffect()\n\t\t\t}\n\t\t} else if (this.isDraggingNativeItem()) {\n\t\t\t// Don't show a nice cursor but still prevent default\n\t\t\t// \"drop and blow away the whole document\" action.\n\t\t\te.preventDefault()\n\t\t} else {\n\t\t\te.preventDefault()\n\t\t\tif (e.dataTransfer) {\n\t\t\t\te.dataTransfer.dropEffect = 'none'\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic handleTopDragLeaveCapture = (e: DragEvent): void => {\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\te.preventDefault()\n\t\t}\n\n\t\tconst isLastLeave = this.enterLeaveCounter.leave(e.target)\n\t\tif (!isLastLeave) {\n\t\t\treturn\n\t\t}\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tsetTimeout(() => this.endDragNativeItem(), 0)\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tpublic handleTopDropCapture = (e: DragEvent): void => {\n\t\tthis.dropTargetIds = []\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\te.preventDefault()\n\t\t\tthis.currentNativeSource?.loadDataTransfer(e.dataTransfer)\n\t\t} else if (matchNativeItemType(e.dataTransfer)) {\n\t\t\t// Dragging some elements, like <a> and <img> may still behave like a native drag event,\n\t\t\t// even if the current drag event matches a user-defined type.\n\t\t\t// Stop the default behavior when we're not expecting a native item to be dropped.\n\n\t\t\te.preventDefault()\n\t\t}\n\n\t\tthis.enterLeaveCounter.reset()\n\t}\n\n\tpublic handleDrop(_e: DragEvent, targetId: string): void {\n\t\tthis.dropTargetIds.unshift(targetId)\n\t}\n\n\tpublic handleTopDrop = (e: DragEvent): void => {\n\t\tconst { dropTargetIds } = this\n\t\tthis.dropTargetIds = []\n\n\t\tthis.actions.hover(dropTargetIds, {\n\t\t\tclientOffset: getEventClientOffset(e),\n\t\t})\n\t\tthis.actions.drop({ dropEffect: this.getCurrentDropEffect() })\n\n\t\tif (this.isDraggingNativeItem()) {\n\t\t\tthis.endDragNativeItem()\n\t\t} else if (this.monitor.isDragging()) {\n\t\t\tthis.actions.endDrag()\n\t\t}\n\t\tthis.cancelHover()\n\t}\n\n\tpublic handleSelectStart = (e: DragEvent): void => {\n\t\tconst target = e.target as HTMLElement & { dragDrop: () => void }\n\n\t\t// Only IE requires us to explicitly say\n\t\t// we want drag drop operation to start\n\t\tif (typeof target.dragDrop !== 'function') {\n\t\t\treturn\n\t\t}\n\n\t\t// Inputs and textareas should be selectable\n\t\tif (\n\t\t\ttarget.tagName === 'INPUT' ||\n\t\t\ttarget.tagName === 'SELECT' ||\n\t\t\ttarget.tagName === 'TEXTAREA' ||\n\t\t\ttarget.isContentEditable\n\t\t) {\n\t\t\treturn\n\t\t}\n\n\t\t// For other targets, ask IE\n\t\t// to enable drag and drop\n\t\te.preventDefault()\n\t\ttarget.dragDrop()\n\t}\n}\n"], "names": ["EnterLeave<PERSON><PERSON>nter", "createNativeDragSource", "matchNativeItemType", "NativeTypes", "getDragPreviewOffset", "getEventClientOffset", "getNodeClientOffset", "OptionsReader", "HTML5BackendImpl", "profile", "sourcePreviewNodes", "size", "sourcePreviewNodeOptions", "sourceNodeOptions", "sourceNodes", "dragStartSourceIds", "length", "dropTargetIds", "dragEnterTargetIds", "dragOverTargetIds", "window", "options", "document", "rootElement", "setup", "root", "undefined", "__isReactDndBackendSetUp", "Error", "addEventListeners", "teardown", "removeEventListeners", "clearCurrentDragSourceNode", "asyncEndDragFrameId", "cancelAnimationFrame", "connectDragPreview", "sourceId", "node", "set", "delete", "connectDragSource", "handleDragStart", "e", "handleSelectStart", "setAttribute", "addEventListener", "removeEventListener", "connectDropTarget", "targetId", "handleDragEnter", "handleDragOver", "handleDrop", "target", "handleTopDragStart", "handleTopDragStartCapture", "handleTopDragEndCapture", "handleTopDragEnter", "handleTopDragEnterCapture", "handleTopDragLeaveCapture", "handleTopDragOver", "handleTopDragOverCapture", "handleTopDrop", "handleTopDropCapture", "getCurrentSourceNodeOptions", "monitor", "getSourceId", "get", "dropEffect", "altKeyPressed", "getCurrentDropEffect", "isDraggingNativeItem", "getCurrentSourcePreviewNodeOptions", "anchorX", "anchorY", "captureDraggingState", "itemType", "getItemType", "Object", "keys", "some", "key", "beginDragNativeItem", "type", "dataTransfer", "currentNativeSource", "currentNative<PERSON><PERSON>le", "registry", "addSource", "actions", "beginDrag", "setCurrentDragSourceNode", "currentDragSourceNode", "MOUSE_MOVE_TIMEOUT", "mouseMoveTimeoutTimer", "setTimeout", "endDragIfSourceWasRemovedFromDOM", "clearTimeout", "defaultPrevented", "unshift", "_e", "manager", "globalContext", "Map", "lastClientOffset", "hoverRafId", "getSourceClientOffset", "source", "endDragNativeItem", "endDrag", "removeSource", "isNodeInDocument", "Boolean", "body", "contains", "isDragging", "cancelHover", "scheduleHover", "requestAnimationFrame", "hover", "clientOffset", "publishSource", "nativeType", "setDragImage", "sourceNode", "dragPreview", "offsetX", "offsetY", "anchorPoint", "offsetPoint", "dragPreviewOffset", "x", "y", "setData", "err", "publishDragSource", "types", "hasAttribute", "preventDefault", "loadDataTransfer", "isFirstEnter", "enterLeaveCounter", "enter", "altKey", "canDrop", "canDropOnTarget", "isLastLeave", "leave", "reset", "drop", "dragDrop", "tagName", "isContentEditable", "getActions", "getMonitor", "getRegistry"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,SAASA,iBAAiB,QAAQ,wBAAwB,CAAA;AAC1D,SACCC,sBAAsB,EACtBC,mBAAmB,QACb,8BAA8B,CAAA;AAErC,YAAYC,WAAW,MAAM,kBAAkB,CAAA;AAC/C,SACCC,oBAAoB,EACpBC,oBAAoB,EACpBC,mBAAmB,QACb,kBAAkB,CAAA;AACzB,SAASC,aAAa,QAAQ,oBAAoB,CAAA;AAKlD,OAAO,MAAMC,gBAAgB;IA0C5B;;IAEG,CACH,AAAOC,OAAO,GAA2B;YAMnB,GAAuB,EAGxB,IAAsB;QAR1C,OAAO;YACNC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACC,IAAI;YAChDC,wBAAwB,EAAE,IAAI,CAACA,wBAAwB,CAACD,IAAI;YAC5DE,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACF,IAAI;YAC9CG,WAAW,EAAE,IAAI,CAACA,WAAW,CAACH,IAAI;YAClCI,kBAAkB,EAAE,CAAA,CAAA,GAAuB,GAAvB,IAAI,CAACA,kBAAkB,cAAvB,GAAuB,WAAQ,GAA/B,KAAA,CAA+B,GAA/B,GAAuB,CAAEC,MAAM,CAAA,IAAI,CAAC;YACxDC,aAAa,EAAE,IAAI,CAACA,aAAa,CAACD,MAAM;YACxCE,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACF,MAAM;YAClDG,iBAAiB,EAAE,CAAA,CAAA,IAAsB,GAAtB,IAAI,CAACA,iBAAiB,cAAtB,IAAsB,WAAQ,GAA9B,KAAA,CAA8B,GAA9B,IAAsB,CAAEH,MAAM,CAAA,IAAI,CAAC;SACtD,CAAA;KACD;IAED,kBAAkB;IAClB,IAAWI,MAAM,GAAuB;QACvC,OAAO,IAAI,CAACC,OAAO,CAACD,MAAM,CAAA;KAC1B;IACD,IAAWE,QAAQ,GAAyB;QAC3C,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAA;KAC5B;IACD;;IAEG,CACH,IAAYC,WAAW,GAAqB;QAC3C,OAAO,IAAI,CAACF,OAAO,CAACE,WAAW,CAAQ;KACvC;IAED,AAAOC,KAAK,GAAS;QACpB,MAAMC,IAAI,GAAG,IAAI,CAACF,WAAW,AAAwB;QACrD,IAAIE,IAAI,KAAKC,SAAS,EAAE;YACvB,OAAM;SACN;QAED,IAAID,IAAI,CAACE,wBAAwB,EAAE;YAClC,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC,CAAA;SACnE;QACDH,IAAI,CAACE,wBAAwB,GAAG,IAAI;QACpC,IAAI,CAACE,iBAAiB,CAACJ,IAAI,CAAC;KAC5B;IAED,AAAOK,QAAQ,GAAS;QACvB,MAAML,IAAI,GAAG,IAAI,CAACF,WAAW,AAAY;QACzC,IAAIE,IAAI,KAAKC,SAAS,EAAE;YACvB,OAAM;SACN;QAEDD,IAAI,CAACE,wBAAwB,GAAG,KAAK;QACrC,IAAI,CAACI,oBAAoB,CAAC,IAAI,CAACR,WAAW,CAAY;QACtD,IAAI,CAACS,0BAA0B,EAAE;QACjC,IAAI,IAAI,CAACC,mBAAmB,EAAE;gBAC7B,GAAW;YAAX,CAAA,GAAW,GAAX,IAAI,CAACb,MAAM,cAAX,GAAW,WAAsB,GAAjC,KAAA,CAAiC,GAAjC,GAAW,CAAEc,oBAAoB,CAAC,IAAI,CAACD,mBAAmB,CAAC,AA3H9D,CA2H8D;SAC3D;KACD;IAED,AAAOE,kBAAkB,CACxBC,QAAgB,EAChBC,IAAa,EACbhB,OAAY,EACE;QACd,IAAI,CAACT,wBAAwB,CAAC0B,GAAG,CAACF,QAAQ,EAAEf,OAAO,CAAC;QACpD,IAAI,CAACX,kBAAkB,CAAC4B,GAAG,CAACF,QAAQ,EAAEC,IAAI,CAAC;QAE3C,OAAO,IAAY;YAClB,IAAI,CAAC3B,kBAAkB,CAAC6B,MAAM,CAACH,QAAQ,CAAC;YACxC,IAAI,CAACxB,wBAAwB,CAAC2B,MAAM,CAACH,QAAQ,CAAC;SAC9C,CAAA;KACD;IAED,AAAOI,iBAAiB,CACvBJ,QAAgB,EAChBC,IAAa,EACbhB,OAAY,EACE;QACd,IAAI,CAACP,WAAW,CAACwB,GAAG,CAACF,QAAQ,EAAEC,IAAI,CAAC;QACpC,IAAI,CAACxB,iBAAiB,CAACyB,GAAG,CAACF,QAAQ,EAAEf,OAAO,CAAC;QAE7C,MAAMoB,eAAe,GAAG,CAACC,CAAM,GAAK,IAAI,CAACD,eAAe,CAACC,CAAC,EAAEN,QAAQ,CAAC;QAAA;QACrE,MAAMO,iBAAiB,GAAG,CAACD,CAAM,GAAK,IAAI,CAACC,iBAAiB,CAACD,CAAC,CAAC;QAAA;QAE/DL,IAAI,CAACO,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;QACtCP,IAAI,CAACQ,gBAAgB,CAAC,WAAW,EAAEJ,eAAe,CAAC;QACnDJ,IAAI,CAACQ,gBAAgB,CAAC,aAAa,EAAEF,iBAAiB,CAAC;QAEvD,OAAO,IAAY;YAClB,IAAI,CAAC7B,WAAW,CAACyB,MAAM,CAACH,QAAQ,CAAC;YACjC,IAAI,CAACvB,iBAAiB,CAAC0B,MAAM,CAACH,QAAQ,CAAC;YAEvCC,IAAI,CAACS,mBAAmB,CAAC,WAAW,EAAEL,eAAe,CAAC;YACtDJ,IAAI,CAACS,mBAAmB,CAAC,aAAa,EAAEH,iBAAiB,CAAC;YAC1DN,IAAI,CAACO,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC;SACvC,CAAA;KACD;IAED,AAAOG,iBAAiB,CAACC,QAAgB,EAAEX,IAAiB,EAAe;QAC1E,MAAMY,eAAe,GAAG,CAACP,CAAY,GAAK,IAAI,CAACO,eAAe,CAACP,CAAC,EAAEM,QAAQ,CAAC;QAAA;QAC3E,MAAME,cAAc,GAAG,CAACR,CAAY,GAAK,IAAI,CAACQ,cAAc,CAACR,CAAC,EAAEM,QAAQ,CAAC;QAAA;QACzE,MAAMG,UAAU,GAAG,CAACT,CAAY,GAAK,IAAI,CAACS,UAAU,CAACT,CAAC,EAAEM,QAAQ,CAAC;QAAA;QAEjEX,IAAI,CAACQ,gBAAgB,CAAC,WAAW,EAAEI,eAAe,CAAC;QACnDZ,IAAI,CAACQ,gBAAgB,CAAC,UAAU,EAAEK,cAAc,CAAC;QACjDb,IAAI,CAACQ,gBAAgB,CAAC,MAAM,EAAEM,UAAU,CAAC;QAEzC,OAAO,IAAY;YAClBd,IAAI,CAACS,mBAAmB,CAAC,WAAW,EAAEG,eAAe,CAAC;YACtDZ,IAAI,CAACS,mBAAmB,CAAC,UAAU,EAAEI,cAAc,CAAC;YACpDb,IAAI,CAACS,mBAAmB,CAAC,MAAM,EAAEK,UAAU,CAAC;SAC5C,CAAA;KACD;IAED,AAAQtB,iBAAiB,CAACuB,MAAY,EAAE;QACvC,2DAA2D;QAC3D,IAAI,CAACA,MAAM,CAACP,gBAAgB,EAAE;YAC7B,OAAM;SACN;QACDO,MAAM,CAACP,gBAAgB,CACtB,WAAW,EACX,IAAI,CAACQ,kBAAkB,CACvB;QACDD,MAAM,CAACP,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACS,yBAAyB,EAAE,IAAI,CAAC;QAC1EF,MAAM,CAACP,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACU,uBAAuB,EAAE,IAAI,CAAC;QACtEH,MAAM,CAACP,gBAAgB,CACtB,WAAW,EACX,IAAI,CAACW,kBAAkB,CACvB;QACDJ,MAAM,CAACP,gBAAgB,CACtB,WAAW,EACX,IAAI,CAACY,yBAAyB,EAC9B,IAAI,CACJ;QACDL,MAAM,CAACP,gBAAgB,CACtB,WAAW,EACX,IAAI,CAACa,yBAAyB,EAC9B,IAAI,CACJ;QACDN,MAAM,CAACP,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACc,iBAAiB,CAAkB;QAC5EP,MAAM,CAACP,gBAAgB,CACtB,UAAU,EACV,IAAI,CAACe,wBAAwB,EAC7B,IAAI,CACJ;QACDR,MAAM,CAACP,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACgB,aAAa,CAAkB;QACpET,MAAM,CAACP,gBAAgB,CACtB,MAAM,EACN,IAAI,CAACiB,oBAAoB,EACzB,IAAI,CACJ;KACD;IAED,AAAQ/B,oBAAoB,CAACqB,MAAY,EAAE;QAC1C,2DAA2D;QAC3D,IAAI,CAACA,MAAM,CAACN,mBAAmB,EAAE;YAChC,OAAM;SACN;QACDM,MAAM,CAACN,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACO,kBAAkB,CAAQ;QACvED,MAAM,CAACN,mBAAmB,CACzB,WAAW,EACX,IAAI,CAACQ,yBAAyB,EAC9B,IAAI,CACJ;QACDF,MAAM,CAACN,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACS,uBAAuB,EAAE,IAAI,CAAC;QACzEH,MAAM,CAACN,mBAAmB,CACzB,WAAW,EACX,IAAI,CAACU,kBAAkB,CACvB;QACDJ,MAAM,CAACN,mBAAmB,CACzB,WAAW,EACX,IAAI,CAACW,yBAAyB,EAC9B,IAAI,CACJ;QACDL,MAAM,CAACN,mBAAmB,CACzB,WAAW,EACX,IAAI,CAACY,yBAAyB,EAC9B,IAAI,CACJ;QACDN,MAAM,CAACN,mBAAmB,CACzB,UAAU,EACV,IAAI,CAACa,iBAAiB,CACtB;QACDP,MAAM,CAACN,mBAAmB,CACzB,UAAU,EACV,IAAI,CAACc,wBAAwB,EAC7B,IAAI,CACJ;QACDR,MAAM,CAACN,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACe,aAAa,CAAkB;QACvET,MAAM,CAACN,mBAAmB,CACzB,MAAM,EACN,IAAI,CAACgB,oBAAoB,EACzB,IAAI,CACJ;KACD;IAED,AAAQC,2BAA2B,GAAG;QACrC,MAAM3B,QAAQ,GAAG,IAAI,CAAC4B,OAAO,CAACC,WAAW,EAAE,AAAU;QACrD,MAAMpD,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACqD,GAAG,CAAC9B,QAAQ,CAAC;QAE9D,OAAO;YACN+B,UAAU,EAAE,IAAI,CAACC,aAAa,GAAG,MAAM,GAAG,MAAM;WAC5CvD,iBAAiB,IAAI,EAAE,CAC3B,CAAA;KACD;IAED,AAAQwD,oBAAoB,GAAG;QAC9B,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;YAChC,gEAAgE;YAChE,OAAO,MAAM,CAAA;SACb;QAED,OAAO,IAAI,CAACP,2BAA2B,EAAE,CAACI,UAAU,CAAA;KACpD;IAED,AAAQI,kCAAkC,GAAG;QAC5C,MAAMnC,QAAQ,GAAG,IAAI,CAAC4B,OAAO,CAACC,WAAW,EAAE,AAAU;QACrD,MAAMrD,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACsD,GAAG,CAAC9B,QAAQ,CAAC;QAE5E,OAAO;YACNoC,OAAO,EAAE,GAAG;YACZC,OAAO,EAAE,GAAG;YACZC,oBAAoB,EAAE,KAAK;WACvB9D,wBAAwB,IAAI,EAAE,CAClC,CAAA;KACD;IAOD,AAAQ0D,oBAAoB,GAAG;QAC9B,MAAMK,QAAQ,GAAG,IAAI,CAACX,OAAO,CAACY,WAAW,EAAE;QAC3C,OAAOC,MAAM,CAACC,IAAI,CAAC3E,WAAW,CAAC,CAAC4E,IAAI,CACnC,CAACC,GAAW,GAAK,AAAC7E,WAAW,AAAQ,CAAC6E,GAAG,CAAC,KAAKL,QAAQ;QAAA,CACvD,CAAA;KACD;IAED,AAAQM,mBAAmB,CAACC,IAAY,EAAEC,YAA2B,EAAE;QACtE,IAAI,CAACnD,0BAA0B,EAAE;QAEjC,IAAI,CAACoD,mBAAmB,GAAGnF,sBAAsB,CAACiF,IAAI,EAAEC,YAAY,CAAC;QACrE,IAAI,CAACE,mBAAmB,GAAG,IAAI,CAACC,QAAQ,CAACC,SAAS,CACjDL,IAAI,EACJ,IAAI,CAACE,mBAAmB,CACxB;QACD,IAAI,CAACI,OAAO,CAACC,SAAS,CAAC;YAAC,IAAI,CAACJ,mBAAmB;SAAC,CAAC;KAClD;IAqCD,AAAQK,wBAAwB,CAACrD,IAAoB,EAAE;QACtD,IAAI,CAACL,0BAA0B,EAAE;QACjC,IAAI,CAAC2D,qBAAqB,GAAGtD,IAAI;QAEjC,oEAAoE;QACpE,OAAO;QACP,sDAAsD;QACtD,wDAAwD;QACxD,MAAMuD,kBAAkB,GAAG,IAAI;QAE/B,gEAAgE;QAChE,oEAAoE;QACpE,oDAAoD;QACpD,EAAE;QACF,kEAAkE;QAClE,kGAAkG;QAClG,gCAAgC;QAChC,EAAE;QACF,OAAO;QACP,sDAAsD;QACtD,wDAAwD;QACxD,EAAE;QACF,IAAI,CAACC,qBAAqB,GAAGC,UAAU,CAAC,IAAM;gBACtC,GAAgB;YAAvB,OAAO,CAAA,GAAgB,GAAhB,IAAI,CAACvE,WAAW,cAAhB,GAAgB,WAAkB,GAAlC,KAAA,CAAkC,GAAlC,GAAgB,CAAEsB,gBAAgB,CACxC,WAAW,EACX,IAAI,CAACkD,gCAAgC,EACrC,IAAI,CACJ,CAAA;SACD,EAAEH,kBAAkB,CAAC,AAAiB;KACvC;IAED,AAAQ5D,0BAA0B,GAAG;QACpC,IAAI,IAAI,CAAC2D,qBAAqB,EAAE;YAC/B,IAAI,CAACA,qBAAqB,GAAG,IAAI;YAEjC,IAAI,IAAI,CAACpE,WAAW,EAAE;oBACrB,GAAW;gBAAX,CAAA,GAAW,GAAX,IAAI,CAACH,MAAM,cAAX,GAAW,WAAc,GAAzB,KAAA,CAAyB,GAAzB,GAAW,CAAE4E,YAAY,CAAC,IAAI,CAACH,qBAAqB,IAAInE,SAAS,CAAC,AArYtE,CAqYsE;gBAClE,IAAI,CAACH,WAAW,CAACuB,mBAAmB,CACnC,WAAW,EACX,IAAI,CAACiD,gCAAgC,EACrC,IAAI,CACJ;aACD;YAED,IAAI,CAACF,qBAAqB,GAAG,IAAI;YACjC,OAAO,IAAI,CAAA;SACX;QAED,OAAO,KAAK,CAAA;KACZ;IAkCD,AAAOpD,eAAe,CAACC,CAAY,EAAEN,QAAgB,EAAQ;QAC5D,IAAIM,CAAC,CAACuD,gBAAgB,EAAE;YACvB,OAAM;SACN;QAED,IAAI,CAAC,IAAI,CAAClF,kBAAkB,EAAE;YAC7B,IAAI,CAACA,kBAAkB,GAAG,EAAE;SAC5B;QACD,IAAI,CAACA,kBAAkB,CAACmF,OAAO,CAAC9D,QAAQ,CAAC;KACzC;IA6ID,AAAOa,eAAe,CAACkD,EAAa,EAAEnD,QAAgB,EAAQ;QAC7D,IAAI,CAAC9B,kBAAkB,CAACgF,OAAO,CAAClD,QAAQ,CAAC;KACzC;IA2CD,AAAOE,cAAc,CAACiD,EAAa,EAAEnD,QAAgB,EAAQ;QAC5D,IAAI,IAAI,CAAC7B,iBAAiB,KAAK,IAAI,EAAE;YACpC,IAAI,CAACA,iBAAiB,GAAG,EAAE;SAC3B;QACD,IAAI,CAACA,iBAAiB,CAAC+E,OAAO,CAAClD,QAAQ,CAAC;KACxC;IA4ED,AAAOG,UAAU,CAACgD,EAAa,EAAEnD,QAAgB,EAAQ;QACxD,IAAI,CAAC/B,aAAa,CAACiF,OAAO,CAAClD,QAAQ,CAAC;KACpC;IAhpBD,YACCoD,OAAwB,EACxBC,aAAmC,EACnChF,OAA6B,CAC5B;QAvBF,KAAQX,kBAAkB,GAAyB,IAAI4F,GAAG,EAAE,AAvC7D,CAuC6D;QAC5D,KAAQ1F,wBAAwB,GAAqB,IAAI0F,GAAG,EAAE,AAxC/D,CAwC+D;QAC9D,KAAQxF,WAAW,GAAyB,IAAIwF,GAAG,EAAE,AAzCtD,CAyCsD;QACrD,KAAQzF,iBAAiB,GAAqB,IAAIyF,GAAG,EAAE,AA1CxD,CA0CwD;QAEvD,KAAQvF,kBAAkB,GAAoB,IAAI,AA5CnD,CA4CmD;QAClD,KAAQE,aAAa,GAAa,EAAE,AA7CrC,CA6CqC;QACpC,KAAQC,kBAAkB,GAAa,EAAE,AA9C1C,CA8C0C;QACzC,KAAQkE,mBAAmB,GAA4B,IAAI,AA/C5D,CA+C4D;QAC3D,KAAQC,mBAAmB,GAAsB,IAAI,AAhDtD,CAgDsD;QACrD,KAAQM,qBAAqB,GAAmB,IAAI,AAjDrD,CAiDqD;QACpD,KAAQvB,aAAa,GAAG,KAAK,AAlD9B,CAkD8B;QAC7B,KAAQyB,qBAAqB,GAAkB,IAAI,AAnDpD,CAmDoD;QACnD,KAAQ5D,mBAAmB,GAAkB,IAAI,AApDlD,CAoDkD;QACjD,KAAQd,iBAAiB,GAAoB,IAAI,AArDlD,CAqDkD;QAEjD,KAAQoF,gBAAgB,GAAmB,IAAI,AAvDhD,CAuDgD;QAC/C,KAAQC,UAAU,GAAkB,IAAI,AAxDzC,CAwDyC;QA+OxC,KAAQC,qBAAqB,GAAG,CAACrE,QAAgB,GAAqB;YACrE,MAAMsE,MAAM,GAAG,IAAI,CAAC5F,WAAW,CAACoD,GAAG,CAAC9B,QAAQ,CAAC;YAC7C,OAAO,AAACsE,MAAM,IAAIpG,mBAAmB,CAACoG,MAAM,CAAgB,IAAK,IAAI,CAAA;SACrE,AA1SF,CA0SE;QAoBD,KAAQC,iBAAiB,GAAG,IAAY;YACvC,IAAI,CAAC,IAAI,CAACrC,oBAAoB,EAAE,EAAE;gBACjC,OAAM;aACN;YAED,IAAI,CAACkB,OAAO,CAACoB,OAAO,EAAE;YACtB,IAAI,IAAI,CAACvB,mBAAmB,EAAE;gBAC7B,IAAI,CAACC,QAAQ,CAACuB,YAAY,CAAC,IAAI,CAACxB,mBAAmB,CAAC;aACpD;YACD,IAAI,CAACA,mBAAmB,GAAG,IAAI;YAC/B,IAAI,CAACD,mBAAmB,GAAG,IAAI;SAC/B,AAzUF,CAyUE;QAED,KAAQ0B,gBAAgB,GAAG,CAACzE,IAA6B,GAAc;YACtE,uEAAuE;YACvE,OAAO0E,OAAO,CACb1E,IAAI,IACH,IAAI,CAACf,QAAQ,IACb,IAAI,CAACA,QAAQ,CAAC0F,IAAI,IAClB,IAAI,CAAC1F,QAAQ,CAAC0F,IAAI,CAACC,QAAQ,CAAC5E,IAAI,CAAC,CAClC,CAAA;SACD,AAnVF,CAmVE;QAED,KAAQ0D,gCAAgC,GAAG,IAAY;YACtD,MAAM1D,IAAI,GAAG,IAAI,CAACsD,qBAAqB;YACvC,IAAItD,IAAI,IAAI,IAAI,IAAI,IAAI,CAACyE,gBAAgB,CAACzE,IAAI,CAAC,EAAE;gBAChD,OAAM;aACN;YAED,IAAI,IAAI,CAACL,0BAA0B,EAAE,IAAI,IAAI,CAACgC,OAAO,CAACkD,UAAU,EAAE,EAAE;gBACnE,IAAI,CAAC1B,OAAO,CAACoB,OAAO,EAAE;aACtB;YACD,IAAI,CAACO,WAAW,EAAE;SAClB,AA/VF,CA+VE;QAqDD,KAAQC,aAAa,GAAG,CAACjG,iBAAkC,GAAK;YAC/D,IACC,IAAI,CAACqF,UAAU,KAAK,IAAI,IACxB,OAAOa,qBAAqB,KAAK,WAAW,EAC3C;gBACD,IAAI,CAACb,UAAU,GAAGa,qBAAqB,CAAC,IAAM;oBAC7C,IAAI,IAAI,CAACrD,OAAO,CAACkD,UAAU,EAAE,EAAE;wBAC9B,IAAI,CAAC1B,OAAO,CAAC8B,KAAK,CAACnG,iBAAiB,IAAI,EAAE,EAAE;4BAC3CoG,YAAY,EAAE,IAAI,CAAChB,gBAAgB;yBACnC,CAAC;qBACF;oBAED,IAAI,CAACC,UAAU,GAAG,IAAI;iBACtB,CAAC;aACF;SACD,AAnaF,CAmaE;QAED,KAAQW,WAAW,GAAG,IAAM;YAC3B,IACC,IAAI,CAACX,UAAU,KAAK,IAAI,IACxB,OAAOtE,oBAAoB,KAAK,WAAW,EAC1C;gBACDA,oBAAoB,CAAC,IAAI,CAACsE,UAAU,CAAC;gBACrC,IAAI,CAACA,UAAU,GAAG,IAAI;aACtB;SACD,AA7aF,CA6aE;QAED,KAAOlD,yBAAyB,GAAG,IAAY;YAC9C,IAAI,CAACtB,0BAA0B,EAAE;YACjC,IAAI,CAACjB,kBAAkB,GAAG,EAAE;SAC5B,AAlbF,CAkbE;QAaD,KAAOsC,kBAAkB,GAAG,CAACX,CAAY,GAAW;YACnD,IAAIA,CAAC,CAACuD,gBAAgB,EAAE;gBACvB,OAAM;aACN;YAED,MAAM,EAAElF,kBAAkB,CAAA,EAAE,GAAG,IAAI;YACnC,IAAI,CAACA,kBAAkB,GAAG,IAAI;YAE9B,MAAMwG,YAAY,GAAGlH,oBAAoB,CAACqC,CAAC,CAAC;YAE5C,qEAAqE;YACrE,IAAI,IAAI,CAACsB,OAAO,CAACkD,UAAU,EAAE,EAAE;gBAC9B,IAAI,CAAC1B,OAAO,CAACoB,OAAO,EAAE;gBACtB,IAAI,CAACO,WAAW,EAAE;aAClB;YAED,oDAAoD;YACpD,IAAI,CAAC3B,OAAO,CAACC,SAAS,CAAC1E,kBAAkB,IAAI,EAAE,EAAE;gBAChDyG,aAAa,EAAE,KAAK;gBACpBf,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;gBACjDc,YAAY;aACZ,CAAC;YAEF,MAAM,EAAEpC,YAAY,CAAA,EAAE,GAAGzC,CAAC;YAC1B,MAAM+E,UAAU,GAAGvH,mBAAmB,CAACiF,YAAY,CAAC;YAEpD,IAAI,IAAI,CAACnB,OAAO,CAACkD,UAAU,EAAE,EAAE;gBAC9B,IAAI/B,YAAY,IAAI,OAAOA,YAAY,CAACuC,YAAY,KAAK,UAAU,EAAE;oBACpE,8CAA8C;oBAC9C,uDAAuD;oBACvD,+DAA+D;oBAC/D,MAAMtF,QAAQ,GAAW,IAAI,CAAC4B,OAAO,CAACC,WAAW,EAAE,AAAU;oBAC7D,MAAM0D,UAAU,GAAG,IAAI,CAAC7G,WAAW,CAACoD,GAAG,CAAC9B,QAAQ,CAAC;oBACjD,MAAMwF,WAAW,GAAG,IAAI,CAAClH,kBAAkB,CAACwD,GAAG,CAAC9B,QAAQ,CAAC,IAAIuF,UAAU;oBAEvE,IAAIC,WAAW,EAAE;wBAChB,MAAM,EAAEpD,OAAO,CAAA,EAAEC,OAAO,CAAA,EAAEoD,OAAO,CAAA,EAAEC,OAAO,CAAA,EAAE,GAC3C,IAAI,CAACvD,kCAAkC,EAAE;wBAC1C,MAAMwD,WAAW,GAAG;4BAAEvD,OAAO;4BAAEC,OAAO;yBAAE;wBACxC,MAAMuD,WAAW,GAAG;4BAAEH,OAAO;4BAAEC,OAAO;yBAAE;wBACxC,MAAMG,iBAAiB,GAAG7H,oBAAoB,CAC7CuH,UAAU,EACVC,WAAW,EACXL,YAAY,EACZQ,WAAW,EACXC,WAAW,CACX;wBAED7C,YAAY,CAACuC,YAAY,CACxBE,WAAW,EACXK,iBAAiB,CAACC,CAAC,EACnBD,iBAAiB,CAACE,CAAC,CACnB;qBACD;iBACD;gBAED,IAAI;oBACH,0CAA0C;oBAC1ChD,YAAY,aAAZA,YAAY,WAAS,GAArBA,KAAAA,CAAqB,GAArBA,YAAY,CAAEiD,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAQ,AAzfxD,CAyfwD;iBACpD,CAAC,OAAOC,GAAG,EAAE;gBACb,2CAA2C;iBAC3C;gBAED,iDAAiD;gBACjD,uDAAuD;gBACvD,IAAI,CAAC3C,wBAAwB,CAAChD,CAAC,CAACU,MAAM,CAAY;gBAElD,+DAA+D;gBAC/D,MAAM,EAAEsB,oBAAoB,CAAA,EAAE,GAAG,IAAI,CAACH,kCAAkC,EAAE;gBAC1E,IAAI,CAACG,oBAAoB,EAAE;oBAC1B,iEAAiE;oBACjE,8DAA8D;oBAC9D,EAAE;oBACF,gEAAgE;oBAChE,mEAAmE;oBACnEoB,UAAU,CAAC,IAAM,IAAI,CAACN,OAAO,CAAC8C,iBAAiB,EAAE;oBAAA,EAAE,CAAC,CAAC;iBACrD,MAAM;oBACN,kEAAkE;oBAClE,yDAAyD;oBACzD,EAAE;oBACF,0DAA0D;oBAC1D,+DAA+D;oBAC/D,+DAA+D;oBAC/D,iEAAiE;oBACjE,wDAAwD;oBACxD,EAAE;oBACF,uDAAuD;oBACvD,IAAI,CAAC9C,OAAO,CAAC8C,iBAAiB,EAAE;iBAChC;aACD,MAAM,IAAIb,UAAU,EAAE;gBACtB,+DAA+D;gBAC/D,IAAI,CAACxC,mBAAmB,CAACwC,UAAU,CAAC;aACpC,MAAM,IACNtC,YAAY,IACZ,CAACA,YAAY,CAACoD,KAAK,IACnB,CAAC,AAAC7F,CAAC,CAACU,MAAM,IAAI,CAAC,AAACV,CAAC,CAACU,MAAM,CAAaoF,YAAY,IAChD,CAAC,AAAC9F,CAAC,CAACU,MAAM,CAAaoF,YAAY,CAAC,WAAW,CAAC,CAAC,EACjD;gBACD,mFAAmF;gBACnF,8EAA8E;gBAC9E,qBAAqB;gBACrB,OAAM;aACN,MAAM;gBACN,oEAAoE;gBACpE9F,CAAC,CAAC+F,cAAc,EAAE;aAClB;SACD,AAziBF,CAyiBE;QAED,KAAOlF,uBAAuB,GAAG,IAAY;YAC5C,IAAI,IAAI,CAACvB,0BAA0B,EAAE,IAAI,IAAI,CAACgC,OAAO,CAACkD,UAAU,EAAE,EAAE;gBACnE,sDAAsD;gBACtD,2DAA2D;gBAC3D,kDAAkD;gBAClD,IAAI,CAAC1B,OAAO,CAACoB,OAAO,EAAE;aACtB;YACD,IAAI,CAACO,WAAW,EAAE;SAClB,AAnjBF,CAmjBE;QAED,KAAO1D,yBAAyB,GAAG,CAACf,CAAY,GAAW;YAC1D,IAAI,CAACxB,kBAAkB,GAAG,EAAE;YAE5B,IAAI,IAAI,CAACoD,oBAAoB,EAAE,EAAE;oBAChC,GAAwB;gBAAxB,CAAA,GAAwB,GAAxB,IAAI,CAACc,mBAAmB,cAAxB,GAAwB,WAAkB,GAA1C,KAAA,CAA0C,GAA1C,GAAwB,CAAEsD,gBAAgB,CAAChG,CAAC,CAACyC,YAAY,CAAC,AAzjB7D,CAyjB6D;aAC1D;YAED,MAAMwD,YAAY,GAAG,IAAI,CAACC,iBAAiB,CAACC,KAAK,CAACnG,CAAC,CAACU,MAAM,CAAC;YAC3D,IAAI,CAACuF,YAAY,IAAI,IAAI,CAAC3E,OAAO,CAACkD,UAAU,EAAE,EAAE;gBAC/C,OAAM;aACN;YAED,MAAM,EAAE/B,YAAY,CAAA,EAAE,GAAGzC,CAAC;YAC1B,MAAM+E,UAAU,GAAGvH,mBAAmB,CAACiF,YAAY,CAAC;YAEpD,IAAIsC,UAAU,EAAE;gBACf,wEAAwE;gBACxE,IAAI,CAACxC,mBAAmB,CAACwC,UAAU,EAAEtC,YAAY,CAAiB;aAClE;SACD,AAxkBF,CAwkBE;QAMD,KAAO3B,kBAAkB,GAAG,CAACd,CAAY,GAAW;YACnD,MAAM,EAAExB,kBAAkB,CAAA,EAAE,GAAG,IAAI;YACnC,IAAI,CAACA,kBAAkB,GAAG,EAAE;YAE5B,IAAI,CAAC,IAAI,CAAC8C,OAAO,CAACkD,UAAU,EAAE,EAAE;gBAC/B,2DAA2D;gBAC3D,OAAM;aACN;YAED,IAAI,CAAC9C,aAAa,GAAG1B,CAAC,CAACoG,MAAM;YAE7B,sFAAsF;YACtF,qFAAqF;YACrF,4DAA4D;YAC5D,IAAI5H,kBAAkB,CAACF,MAAM,GAAG,CAAC,EAAE;gBAClC,IAAI,CAACwE,OAAO,CAAC8B,KAAK,CAACpG,kBAAkB,EAAE;oBACtCqG,YAAY,EAAElH,oBAAoB,CAACqC,CAAC,CAAC;iBACrC,CAAC;aACF;YAED,MAAMqG,OAAO,GAAG7H,kBAAkB,CAAC6D,IAAI,CAAC,CAAC/B,QAAQ,GAChD,IAAI,CAACgB,OAAO,CAACgF,eAAe,CAAChG,QAAQ,CAAC;YAAA,CACtC;YAED,IAAI+F,OAAO,EAAE;gBACZ,2CAA2C;gBAC3CrG,CAAC,CAAC+F,cAAc,EAAE;gBAClB,IAAI/F,CAAC,CAACyC,YAAY,EAAE;oBACnBzC,CAAC,CAACyC,YAAY,CAAChB,UAAU,GAAG,IAAI,CAACE,oBAAoB,EAAE;iBACvD;aACD;SACD,AA7mBF,CA6mBE;QAED,KAAOT,wBAAwB,GAAG,CAAClB,CAAY,GAAW;YACzD,IAAI,CAACvB,iBAAiB,GAAG,EAAE;YAE3B,IAAI,IAAI,CAACmD,oBAAoB,EAAE,EAAE;oBAChC,GAAwB;gBAAxB,CAAA,GAAwB,GAAxB,IAAI,CAACc,mBAAmB,cAAxB,GAAwB,WAAkB,GAA1C,KAAA,CAA0C,GAA1C,GAAwB,CAAEsD,gBAAgB,CAAChG,CAAC,CAACyC,YAAY,CAAC,AAnnB7D,CAmnB6D;aAC1D;SACD,AArnBF,CAqnBE;QASD,KAAOxB,iBAAiB,GAAG,CAACjB,CAAY,GAAW;YAClD,MAAM,EAAEvB,iBAAiB,CAAA,EAAE,GAAG,IAAI;YAClC,IAAI,CAACA,iBAAiB,GAAG,EAAE;YAE3B,IAAI,CAAC,IAAI,CAAC6C,OAAO,CAACkD,UAAU,EAAE,EAAE;gBAC/B,2DAA2D;gBAC3D,kEAAkE;gBAClExE,CAAC,CAAC+F,cAAc,EAAE;gBAClB,IAAI/F,CAAC,CAACyC,YAAY,EAAE;oBACnBzC,CAAC,CAACyC,YAAY,CAAChB,UAAU,GAAG,MAAM;iBAClC;gBACD,OAAM;aACN;YAED,IAAI,CAACC,aAAa,GAAG1B,CAAC,CAACoG,MAAM;YAC7B,IAAI,CAACvC,gBAAgB,GAAGlG,oBAAoB,CAACqC,CAAC,CAAC;YAE/C,IAAI,CAAC0E,aAAa,CAACjG,iBAAiB,CAAC;YAErC,MAAM4H,OAAO,GAAG,CAAC5H,iBAAiB,IAAI,EAAE,CAAC,CAAC4D,IAAI,CAAC,CAAC/B,QAAQ,GACvD,IAAI,CAACgB,OAAO,CAACgF,eAAe,CAAChG,QAAQ,CAAC;YAAA,CACtC;YAED,IAAI+F,OAAO,EAAE;gBACZ,mCAAmC;gBACnCrG,CAAC,CAAC+F,cAAc,EAAE;gBAClB,IAAI/F,CAAC,CAACyC,YAAY,EAAE;oBACnBzC,CAAC,CAACyC,YAAY,CAAChB,UAAU,GAAG,IAAI,CAACE,oBAAoB,EAAE;iBACvD;aACD,MAAM,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;gBACvC,qDAAqD;gBACrD,kDAAkD;gBAClD5B,CAAC,CAAC+F,cAAc,EAAE;aAClB,MAAM;gBACN/F,CAAC,CAAC+F,cAAc,EAAE;gBAClB,IAAI/F,CAAC,CAACyC,YAAY,EAAE;oBACnBzC,CAAC,CAACyC,YAAY,CAAChB,UAAU,GAAG,MAAM;iBAClC;aACD;SACD,AArqBF,CAqqBE;QAED,KAAOT,yBAAyB,GAAG,CAAChB,CAAY,GAAW;YAC1D,IAAI,IAAI,CAAC4B,oBAAoB,EAAE,EAAE;gBAChC5B,CAAC,CAAC+F,cAAc,EAAE;aAClB;YAED,MAAMQ,WAAW,GAAG,IAAI,CAACL,iBAAiB,CAACM,KAAK,CAACxG,CAAC,CAACU,MAAM,CAAC;YAC1D,IAAI,CAAC6F,WAAW,EAAE;gBACjB,OAAM;aACN;YAED,IAAI,IAAI,CAAC3E,oBAAoB,EAAE,EAAE;gBAChCwB,UAAU,CAAC,IAAM,IAAI,CAACa,iBAAiB,EAAE;gBAAA,EAAE,CAAC,CAAC;aAC7C;YACD,IAAI,CAACQ,WAAW,EAAE;SAClB,AArrBF,CAqrBE;QAED,KAAOrD,oBAAoB,GAAG,CAACpB,CAAY,GAAW;YACrD,IAAI,CAACzB,aAAa,GAAG,EAAE;YAEvB,IAAI,IAAI,CAACqD,oBAAoB,EAAE,EAAE;oBAEhC,GAAwB;gBADxB5B,CAAC,CAAC+F,cAAc,EAAE;gBAClB,CAAA,GAAwB,GAAxB,IAAI,CAACrD,mBAAmB,cAAxB,GAAwB,WAAkB,GAA1C,KAAA,CAA0C,GAA1C,GAAwB,CAAEsD,gBAAgB,CAAChG,CAAC,CAACyC,YAAY,CAAC,AA5rB7D,CA4rB6D;aAC1D,MAAM,IAAIjF,mBAAmB,CAACwC,CAAC,CAACyC,YAAY,CAAC,EAAE;gBAC/C,wFAAwF;gBACxF,8DAA8D;gBAC9D,kFAAkF;gBAElFzC,CAAC,CAAC+F,cAAc,EAAE;aAClB;YAED,IAAI,CAACG,iBAAiB,CAACO,KAAK,EAAE;SAC9B,AAtsBF,CAssBE;QAMD,KAAOtF,aAAa,GAAG,CAACnB,CAAY,GAAW;YAC9C,MAAM,EAAEzB,aAAa,CAAA,EAAE,GAAG,IAAI;YAC9B,IAAI,CAACA,aAAa,GAAG,EAAE;YAEvB,IAAI,CAACuE,OAAO,CAAC8B,KAAK,CAACrG,aAAa,EAAE;gBACjCsG,YAAY,EAAElH,oBAAoB,CAACqC,CAAC,CAAC;aACrC,CAAC;YACF,IAAI,CAAC8C,OAAO,CAAC4D,IAAI,CAAC;gBAAEjF,UAAU,EAAE,IAAI,CAACE,oBAAoB,EAAE;aAAE,CAAC;YAE9D,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;gBAChC,IAAI,CAACqC,iBAAiB,EAAE;aACxB,MAAM,IAAI,IAAI,CAAC3C,OAAO,CAACkD,UAAU,EAAE,EAAE;gBACrC,IAAI,CAAC1B,OAAO,CAACoB,OAAO,EAAE;aACtB;YACD,IAAI,CAACO,WAAW,EAAE;SAClB,AA3tBF,CA2tBE;QAED,KAAOxE,iBAAiB,GAAG,CAACD,CAAY,GAAW;YAClD,MAAMU,MAAM,GAAGV,CAAC,CAACU,MAAM,AAA0C;YAEjE,wCAAwC;YACxC,uCAAuC;YACvC,IAAI,OAAOA,MAAM,CAACiG,QAAQ,KAAK,UAAU,EAAE;gBAC1C,OAAM;aACN;YAED,4CAA4C;YAC5C,IACCjG,MAAM,CAACkG,OAAO,KAAK,OAAO,IAC1BlG,MAAM,CAACkG,OAAO,KAAK,QAAQ,IAC3BlG,MAAM,CAACkG,OAAO,KAAK,UAAU,IAC7BlG,MAAM,CAACmG,iBAAiB,EACvB;gBACD,OAAM;aACN;YAED,4BAA4B;YAC5B,0BAA0B;YAC1B7G,CAAC,CAAC+F,cAAc,EAAE;YAClBrF,MAAM,CAACiG,QAAQ,EAAE;SACjB,AApvBF,CAovBE;QArrBA,IAAI,CAAChI,OAAO,GAAG,IAAId,aAAa,CAAC8F,aAAa,EAAEhF,OAAO,CAAC;QACxD,IAAI,CAACmE,OAAO,GAAGY,OAAO,CAACoD,UAAU,EAAE;QACnC,IAAI,CAACxF,OAAO,GAAGoC,OAAO,CAACqD,UAAU,EAAE;QACnC,IAAI,CAACnE,QAAQ,GAAGc,OAAO,CAACsD,WAAW,EAAE;QACrC,IAAI,CAACd,iBAAiB,GAAG,IAAI5I,iBAAiB,CAAC,IAAI,CAAC8G,gBAAgB,CAAC;KACrE;CAirBD"}