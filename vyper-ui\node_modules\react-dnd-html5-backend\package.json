{"_from": "react-dnd-html5-backend", "_id": "react-dnd-html5-backend@16.0.1", "_inBundle": false, "_integrity": "sha512-Wu3dw5aDJmOGw8WjH1I1/yTH+vlXEL4vmjk5p+MHxP8HuHJS1lAGeIdG/hze1AvNeXWo/JgULV87LyQOr+r5jw==", "_location": "/react-dnd-html5-backend", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "react-dnd-html5-backend", "name": "react-dnd-html5-backend", "escapedName": "react-dnd-html5-backend", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/react-dnd-html5-backend/-/react-dnd-html5-backend-16.0.1.tgz", "_shasum": "87faef15845d512a23b3c08d29ecfd34871688b6", "_spec": "react-dnd-html5-backend", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/react-dnd/react-dnd/issues"}, "bundleDependencies": false, "dependencies": {"dnd-core": "^16.0.1"}, "deprecated": false, "description": "HTML5 backend for React DnD", "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.2.168", "@types/jest": "^27.4.1", "npm-run-all": "^4.1.5", "shx": "^0.3.4", "typescript": "^4.6.3"}, "homepage": "https://github.com/react-dnd/react-dnd#readme", "license": "MIT", "main": "dist/index.js", "name": "react-dnd-html5-backend", "repository": {"type": "git", "url": "git+https://github.com/react-dnd/react-dnd.git"}, "scripts": {"build": "run-s build_types build_esm", "build_esm": "swc -C module.type=es6 -d dist src/", "build_types": "tsc -b .", "clean": "shx rm -rf dist/"}, "sideEffects": false, "type": "module", "types": "dist/index.d.ts", "version": "16.0.1"}