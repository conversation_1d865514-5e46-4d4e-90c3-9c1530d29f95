{"version": 3, "sources": ["../../../src/hooks/useDrag/useDrag.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tConnectDragPreview,\n\tConnectDragSource,\n} from '../../types/index.js'\nimport type { DragSourceHookSpec, FactoryOrInstance } from '../types.js'\nimport { useCollectedProps } from '../useCollectedProps.js'\nimport { useOptionalFactory } from '../useOptionalFactory.js'\nimport { useConnectDragPreview, useConnectDragSource } from './connectors.js'\nimport { useDragSourceConnector } from './useDragSourceConnector.js'\nimport { useDragSourceMonitor } from './useDragSourceMonitor.js'\nimport { useRegisteredDragSource } from './useRegisteredDragSource.js'\n\n/**\n * useDragSource hook\n * @param sourceSpec The drag source specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */\nexport function useDrag<\n\tDragObject = unknown,\n\tDropResult = unknown,\n\tCollectedProps = unknown,\n>(\n\tspecArg: FactoryOrInstance<\n\t\tDragSourceHookSpec<DragObject, DropResult, CollectedProps>\n\t>,\n\tdeps?: unknown[],\n): [CollectedProps, ConnectDragSource, ConnectDragPreview] {\n\tconst spec = useOptionalFactory(specArg, deps)\n\tinvariant(\n\t\t!(spec as any).begin,\n\t\t`useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)`,\n\t)\n\n\tconst monitor = useDragSourceMonitor<DragObject, DropResult>()\n\tconst connector = useDragSourceConnector(spec.options, spec.previewOptions)\n\tuseRegisteredDragSource(spec, monitor, connector)\n\n\treturn [\n\t\tuseCollectedProps(spec.collect, monitor, connector),\n\t\tuseConnectDragSource(connector),\n\t\tuseConnectDragPreview(connector),\n\t]\n}\n"], "names": ["invariant", "useCollectedProps", "useOptionalFactory", "useConnectDragPreview", "useConnectDragSource", "useDragSourceConnector", "useDragSourceMonitor", "useRegisteredDragSource", "useDrag", "specArg", "deps", "spec", "begin", "monitor", "connector", "options", "previewOptions", "collect"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAOhD,SAASC,iBAAiB,QAAQ,yBAAyB,CAAA;AAC3D,SAASC,kBAAkB,QAAQ,0BAA0B,CAAA;AAC7D,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,iBAAiB,CAAA;AAC7E,SAASC,sBAAsB,QAAQ,6BAA6B,CAAA;AACpE,SAASC,oBAAoB,QAAQ,2BAA2B,CAAA;AAChE,SAASC,uBAAuB,QAAQ,8BAA8B,CAAA;AAEtE;;;;GAIG,CACH,OAAO,SAASC,OAAO,CAKtBC,OAEC,EACDC,IAAgB,EAC0C;IAC1D,MAAMC,IAAI,GAAGT,kBAAkB,CAACO,OAAO,EAAEC,IAAI,CAAC;IAC9CV,SAAS,CACR,CAAC,AAACW,IAAI,CAASC,KAAK,EACpB,CAAC,2JAA2J,CAAC,CAC7J;IAED,MAAMC,OAAO,GAAGP,oBAAoB,EAA0B;IAC9D,MAAMQ,SAAS,GAAGT,sBAAsB,CAACM,IAAI,CAACI,OAAO,EAAEJ,IAAI,CAACK,cAAc,CAAC;IAC3ET,uBAAuB,CAACI,IAAI,EAAEE,OAAO,EAAEC,SAAS,CAAC;IAEjD,OAAO;QACNb,iBAAiB,CAACU,IAAI,CAACM,OAAO,EAAEJ,OAAO,EAAEC,SAAS,CAAC;QACnDV,oBAAoB,CAACU,SAAS,CAAC;QAC/BX,qBAAqB,CAACW,SAAS,CAAC;KAChC,CAAA;CACD"}