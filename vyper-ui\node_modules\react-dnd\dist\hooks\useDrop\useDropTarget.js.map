{"version": 3, "sources": ["../../../src/hooks/useDrop/useDropTarget.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react'\n\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport type { DropTargetHookSpec } from '../types.js'\nimport { DropTargetImpl } from './DropTargetImpl.js'\n\nexport function useDropTarget<O, R, P>(\n\tspec: DropTargetHookSpec<O, R, P>,\n\tmonitor: DropTargetMonitor<O, R>,\n) {\n\tconst dropTarget = useMemo(() => new DropTargetImpl(spec, monitor), [monitor])\n\tuseEffect(() => {\n\t\tdropTarget.spec = spec\n\t}, [spec])\n\treturn dropTarget\n}\n"], "names": ["useEffect", "useMemo", "DropTargetImpl", "useDropTarget", "spec", "monitor", "drop<PERSON>ar<PERSON>"], "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,QAAQ,OAAO,CAAA;AAI1C,SAASC,cAAc,QAAQ,qBAAqB,CAAA;AAEpD,OAAO,SAASC,aAAa,CAC5BC,IAAiC,EACjCC,OAAgC,EAC/B;IACD,MAAMC,UAAU,GAAGL,OAAO,CAAC,IAAM,IAAIC,cAAc,CAACE,IAAI,EAAEC,OAAO,CAAC;IAAA,EAAE;QAACA,OAAO;KAAC,CAAC;IAC9EL,SAAS,CAAC,IAAM;QACfM,UAAU,CAACF,IAAI,GAAGA,IAAI;KACtB,EAAE;QAACA,IAAI;KAAC,CAAC;IACV,OAAOE,UAAU,CAAA;CACjB"}