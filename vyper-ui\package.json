{"name": "vyper-ui", "version": "2.3.4", "private": true, "dependencies": {"@date-io/date-fns": "^1.3.13", "@material-ui/core": "^4.12.3", "@material-ui/icons": "^4.11.2", "@material-ui/lab": "^4.0.0-alpha.60", "@material-ui/pickers": "^3.3.10", "@material-ui/styles": "^4.11.5", "abortcontroller-polyfill": "^1.7.3", "ag-grid-community": "^27.3.0", "ag-grid-react": "^27.3.0", "axios": "^0.27.2", "clsx": "^1.1.1", "core-js": "^3.6.5", "cra-template": "1.1.2", "env-cmd": "^8.0.2", "es6-promise": "^4.2.8", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "fontsource-roboto": "^4.0.0", "formik": "^2.2.9", "formik-material-ui": "^2.0.1", "history": "^4.10.1", "html-to-react": "^1.4.7", "immer": "^9.0.7", "material-table": "^1.69.3", "moment": "^2.29.1", "notistack": "^1.0.10", "prop-types": "^15.7.2", "raf": "^3.4.1", "react": "^16.14.0", "react-app-polyfill": "^1.0.6", "react-datasheet-grid": "^4.11.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^16.14.0", "react-file-reader-input": "^2.0.0", "react-moment": "^1.1.1", "react-query": "^3.39.2", "react-router-dom": "^5.3.0", "reactstrap": "^8.9.0", "src": "^1.1.2", "ti-fetch": "^1.0.0", "url-search-params-polyfill": "^8.1.1", "whatwg-fetch": "^3.6.2", "yup": "^0.32.11"}, "scripts": {"start": "cross-env REACT_APP_ENV=localhost webpack-dev-server", "build": "webpack --mode=production", "test": "cross-env BABEL_ENV=test jest", "eject": "react-scripts eject", "build:dev": "cross-env REACT_APP_ENV=dev NODE_OPTIONS=--openssl-legacy-provider webpack --mode=production", "build:stage": "cross-env REACT_APP_ENV=stage NODE_OPTIONS=--openssl-legacy-provider webpack --mode=production", "build:prod": "cross-env REACT_APP_ENV=prod NODE_OPTIONS=--openssl-legacy-provider webpack --mode=production", "build:extdev": "cross-env REACT_APP_ENV=ext-dev NODE_OPTIONS=--openssl-legacy-provider webpack --mode=production", "build:extstage": "cross-env REACT_APP_ENV=ext-stage NODE_OPTIONS=--openssl-legacy-provider webpack --mode=production", "build:extprod": "cross-env REACT_APP_ENV=ext-prod NODE_OPTIONS=--openssl-legacy-provider webpack --mode=production", "analyze": "webpack --mode=production --env.analyze=true", "lint": "eslint src --ext js", "format": "prettier --write './**'", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "coverage": "cross-env BABEL_ENV=test jest --coverage", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json}\""}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "ie 11"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "ie 11"]}, "proxy": "http://localhost:10001", "homepage": "/vyper/", "husky": {"hooks": {}}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-transform-runtime": "^7.16.4", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@babel/runtime": "^7.16.3", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@types/jest": "^27.0.3", "@types/systemjs": "^6.1.1", "babel-eslint": "^10.1.0", "babel-jest": "^27.4.2", "concurrently": "^6.4.0", "cross-env": "^7.0.3", "eslint": "^8.4.1", "eslint-config-prettier": "^8.3.0", "eslint-config-react-important-stuff": "^3.0.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.3.0", "file-loader": "^6.2.0", "husky": "^7.0.4", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.3", "jest-cli": "^27.4.3", "prettier": "^2.5.1", "pretty-quick": "^3.1.2", "single-spa-react": "^4.6.0", "systemjs-webpack-interop": "^1.2.1", "webpack": "^4.46.0", "webpack-cli": "^3.3.10", "webpack-config-single-spa-react": "^1.0.3", "webpack-dev-server": "^3.9.0", "webpack-merge": "^4.2.2"}}