{"version": 3, "sources": ["../../src/NativeDragSources/NativeDragSource.ts"], "sourcesContent": ["import type { DragDropMonitor } from 'dnd-core'\n\nimport type { NativeItemConfig } from './nativeTypesConfig.js'\n\nexport class NativeDragSource {\n\tpublic item: any\n\tprivate config: NativeItemConfig\n\n\tpublic constructor(config: NativeItemConfig) {\n\t\tthis.config = config\n\t\tthis.item = {}\n\t\tthis.initializeExposedProperties()\n\t}\n\n\tprivate initializeExposedProperties() {\n\t\tObject.keys(this.config.exposeProperties).forEach((property) => {\n\t\t\tObject.defineProperty(this.item, property, {\n\t\t\t\tconfigurable: true, // This is needed to allow redefining it later\n\t\t\t\tenumerable: true,\n\t\t\t\tget() {\n\t\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t`Browser doesn't allow reading \"${property}\" until the drop event.`,\n\t\t\t\t\t)\n\t\t\t\t\treturn null\n\t\t\t\t},\n\t\t\t})\n\t\t})\n\t}\n\n\tpublic loadDataTransfer(dataTransfer: DataTransfer | null | undefined): void {\n\t\tif (dataTransfer) {\n\t\t\tconst newProperties: PropertyDescriptorMap = {}\n\t\t\tObject.keys(this.config.exposeProperties).forEach((property) => {\n\t\t\t\tconst propertyFn = this.config.exposeProperties[property]\n\t\t\t\tif (propertyFn != null) {\n\t\t\t\t\tnewProperties[property] = {\n\t\t\t\t\t\tvalue: propertyFn(dataTransfer, this.config.matchesTypes),\n\t\t\t\t\t\tconfigurable: true,\n\t\t\t\t\t\tenumerable: true,\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t\tObject.defineProperties(this.item, newProperties)\n\t\t}\n\t}\n\n\tpublic canDrag(): boolean {\n\t\treturn true\n\t}\n\n\tpublic beginDrag(): any {\n\t\treturn this.item\n\t}\n\n\tpublic isDragging(monitor: DragDropMonitor, handle: string): boolean {\n\t\treturn handle === monitor.getSourceId()\n\t}\n\n\tpublic endDrag(): void {\n\t\t// empty\n\t}\n}\n"], "names": ["NativeDragSource", "initializeExposedProperties", "Object", "keys", "config", "exposeProperties", "for<PERSON>ach", "property", "defineProperty", "item", "configurable", "enumerable", "get", "console", "warn", "loadDataTransfer", "dataTransfer", "newProperties", "propertyFn", "value", "matchesTypes", "defineProperties", "canDrag", "beginDrag", "isDragging", "monitor", "handle", "getSourceId", "endDrag"], "mappings": "AAIA,OAAO,MAAMA,gBAAgB;IAU5B,AAAQC,2BAA2B,GAAG;QACrCC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACC,MAAM,CAACC,gBAAgB,CAAC,CAACC,OAAO,CAAC,CAACC,QAAQ,GAAK;YAC/DL,MAAM,CAACM,cAAc,CAAC,IAAI,CAACC,IAAI,EAAEF,QAAQ,EAAE;gBAC1CG,YAAY,EAAE,IAAI;gBAClBC,UAAU,EAAE,IAAI;gBAChBC,GAAG,IAAG;oBACL,sCAAsC;oBACtCC,OAAO,CAACC,IAAI,CACX,CAAC,+BAA+B,EAAEP,QAAQ,CAAC,uBAAuB,CAAC,CACnE;oBACD,OAAO,IAAI,CAAA;iBACX;aACD,CAAC;SACF,CAAC;KACF;IAED,AAAOQ,gBAAgB,CAACC,YAA6C,EAAQ;QAC5E,IAAIA,YAAY,EAAE;YACjB,MAAMC,aAAa,GAA0B,EAAE;YAC/Cf,MAAM,CAACC,IAAI,CAAC,IAAI,CAACC,MAAM,CAACC,gBAAgB,CAAC,CAACC,OAAO,CAAC,CAACC,QAAQ,GAAK;gBAC/D,MAAMW,UAAU,GAAG,IAAI,CAACd,MAAM,CAACC,gBAAgB,CAACE,QAAQ,CAAC;gBACzD,IAAIW,UAAU,IAAI,IAAI,EAAE;oBACvBD,aAAa,CAACV,QAAQ,CAAC,GAAG;wBACzBY,KAAK,EAAED,UAAU,CAACF,YAAY,EAAE,IAAI,CAACZ,MAAM,CAACgB,YAAY,CAAC;wBACzDV,YAAY,EAAE,IAAI;wBAClBC,UAAU,EAAE,IAAI;qBAChB;iBACD;aACD,CAAC;YACFT,MAAM,CAACmB,gBAAgB,CAAC,IAAI,CAACZ,IAAI,EAAEQ,aAAa,CAAC;SACjD;KACD;IAED,AAAOK,OAAO,GAAY;QACzB,OAAO,IAAI,CAAA;KACX;IAED,AAAOC,SAAS,GAAQ;QACvB,OAAO,IAAI,CAACd,IAAI,CAAA;KAChB;IAED,AAAOe,UAAU,CAACC,OAAwB,EAAEC,MAAc,EAAW;QACpE,OAAOA,MAAM,KAAKD,OAAO,CAACE,WAAW,EAAE,CAAA;KACvC;IAED,AAAOC,OAAO,GAAS;IACtB,QAAQ;KACR;IArDD,YAAmBxB,MAAwB,CAAE;QAC5C,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACK,IAAI,GAAG,EAAE;QACd,IAAI,CAACR,2BAA2B,EAAE;KAClC;CAkDD"}