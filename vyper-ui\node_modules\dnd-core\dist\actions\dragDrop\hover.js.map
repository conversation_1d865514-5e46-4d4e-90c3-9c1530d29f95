{"version": 3, "sources": ["../../../src/actions/dragDrop/hover.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tHoverOptions,\n\tHoverPayload,\n\tIdentifier,\n} from '../../interfaces.js'\nimport { matchesType } from '../../utils/matchesType.js'\nimport { HOVER } from './types.js'\n\nexport function createHover(manager: DragDropManager) {\n\treturn function hover(\n\t\ttargetIdsArg: string[],\n\t\t{ clientOffset }: HoverOptions = {},\n\t): Action<HoverPayload> {\n\t\tverifyTargetIdsIsArray(targetIdsArg)\n\t\tconst targetIds = targetIdsArg.slice(0)\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tconst draggedItemType = monitor.getItemType()\n\t\tremoveNonMatchingTargetIds(targetIds, registry, draggedItemType)\n\t\tcheckInvariants(targetIds, monitor, registry)\n\t\thoverAllTargets(targetIds, monitor, registry)\n\n\t\treturn {\n\t\t\ttype: HOVER,\n\t\t\tpayload: {\n\t\t\t\ttargetIds,\n\t\t\t\tclientOffset: clientOffset || null,\n\t\t\t},\n\t\t}\n\t}\n}\n\nfunction verifyTargetIdsIsArray(targetIdsArg: string[]) {\n\tinvariant(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.')\n}\n\nfunction checkInvariants(\n\ttargetIds: string[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\tinvariant(monitor.isDragging(), 'Cannot call hover while not dragging.')\n\tinvariant(!monitor.didDrop(), 'Cannot call hover after drop.')\n\tfor (let i = 0; i < targetIds.length; i++) {\n\t\tconst targetId = targetIds[i] as string\n\t\tinvariant(\n\t\t\ttargetIds.lastIndexOf(targetId) === i,\n\t\t\t'Expected targetIds to be unique in the passed array.',\n\t\t)\n\n\t\tconst target = registry.getTarget(targetId)\n\t\tinvariant(target, 'Expected targetIds to be registered.')\n\t}\n}\n\nfunction removeNonMatchingTargetIds(\n\ttargetIds: string[],\n\tregistry: HandlerRegistry,\n\tdraggedItemType: Identifier | null,\n) {\n\t// Remove those targetIds that don't match the targetType.  This\n\t// fixes shallow isOver which would only be non-shallow because of\n\t// non-matching targets.\n\tfor (let i = targetIds.length - 1; i >= 0; i--) {\n\t\tconst targetId = targetIds[i] as string\n\t\tconst targetType = registry.getTargetType(targetId)\n\t\tif (!matchesType(targetType, draggedItemType)) {\n\t\t\ttargetIds.splice(i, 1)\n\t\t}\n\t}\n}\n\nfunction hoverAllTargets(\n\ttargetIds: string[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\t// Finally call hover on all matching targets.\n\ttargetIds.forEach(function (targetId) {\n\t\tconst target = registry.getTarget(targetId)\n\t\ttarget.hover(monitor, targetId)\n\t})\n}\n"], "names": ["invariant", "matchesType", "HOVER", "createHover", "manager", "hover", "targetIdsArg", "clientOffset", "verifyTargetIdsIsArray", "targetIds", "slice", "monitor", "getMonitor", "registry", "getRegistry", "draggedItemType", "getItemType", "removeNonMatchingTargetIds", "checkInvariants", "hoverAllTargets", "type", "payload", "Array", "isArray", "isDragging", "didDrop", "i", "length", "targetId", "lastIndexOf", "target", "get<PERSON><PERSON><PERSON>", "targetType", "getTargetType", "splice", "for<PERSON>ach"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAWhD,SAASC,WAAW,QAAQ,4BAA4B,CAAA;AACxD,SAASC,KAAK,QAAQ,YAAY,CAAA;AAElC,OAAO,SAASC,WAAW,CAACC,OAAwB,EAAE;IACrD,OAAO,SAASC,KAAK,CACpBC,YAAsB,EACtB,EAAEC,YAAY,CAAA,EAAgB,GAAG,EAAE,EACZ;QACvBC,sBAAsB,CAACF,YAAY,CAAC;QACpC,MAAMG,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC,CAAC;QACvC,MAAMC,OAAO,GAAGP,OAAO,CAACQ,UAAU,EAAE;QACpC,MAAMC,QAAQ,GAAGT,OAAO,CAACU,WAAW,EAAE;QACtC,MAAMC,eAAe,GAAGJ,OAAO,CAACK,WAAW,EAAE;QAC7CC,0BAA0B,CAACR,SAAS,EAAEI,QAAQ,EAAEE,eAAe,CAAC;QAChEG,eAAe,CAACT,SAAS,EAAEE,OAAO,EAAEE,QAAQ,CAAC;QAC7CM,eAAe,CAACV,SAAS,EAAEE,OAAO,EAAEE,QAAQ,CAAC;QAE7C,OAAO;YACNO,IAAI,EAAElB,KAAK;YACXmB,OAAO,EAAE;gBACRZ,SAAS;gBACTF,YAAY,EAAEA,YAAY,IAAI,IAAI;aAClC;SACD,CAAA;KACD,CAAA;CACD;AAED,SAASC,sBAAsB,CAACF,YAAsB,EAAE;IACvDN,SAAS,CAACsB,KAAK,CAACC,OAAO,CAACjB,YAAY,CAAC,EAAE,oCAAoC,CAAC;CAC5E;AAED,SAASY,eAAe,CACvBT,SAAmB,EACnBE,OAAwB,EACxBE,QAAyB,EACxB;IACDb,SAAS,CAACW,OAAO,CAACa,UAAU,EAAE,EAAE,uCAAuC,CAAC;IACxExB,SAAS,CAAC,CAACW,OAAO,CAACc,OAAO,EAAE,EAAE,+BAA+B,CAAC;IAC9D,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,SAAS,CAACkB,MAAM,EAAED,CAAC,EAAE,CAAE;QAC1C,MAAME,QAAQ,GAAGnB,SAAS,CAACiB,CAAC,CAAC,AAAU;QACvC1B,SAAS,CACRS,SAAS,CAACoB,WAAW,CAACD,QAAQ,CAAC,KAAKF,CAAC,EACrC,sDAAsD,CACtD;QAED,MAAMI,MAAM,GAAGjB,QAAQ,CAACkB,SAAS,CAACH,QAAQ,CAAC;QAC3C5B,SAAS,CAAC8B,MAAM,EAAE,sCAAsC,CAAC;KACzD;CACD;AAED,SAASb,0BAA0B,CAClCR,SAAmB,EACnBI,QAAyB,EACzBE,eAAkC,EACjC;IACD,gEAAgE;IAChE,kEAAkE;IAClE,wBAAwB;IACxB,IAAK,IAAIW,CAAC,GAAGjB,SAAS,CAACkB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAE;QAC/C,MAAME,QAAQ,GAAGnB,SAAS,CAACiB,CAAC,CAAC,AAAU;QACvC,MAAMM,UAAU,GAAGnB,QAAQ,CAACoB,aAAa,CAACL,QAAQ,CAAC;QACnD,IAAI,CAAC3B,WAAW,CAAC+B,UAAU,EAAEjB,eAAe,CAAC,EAAE;YAC9CN,SAAS,CAACyB,MAAM,CAACR,CAAC,EAAE,CAAC,CAAC;SACtB;KACD;CACD;AAED,SAASP,eAAe,CACvBV,SAAmB,EACnBE,OAAwB,EACxBE,QAAyB,EACxB;IACD,8CAA8C;IAC9CJ,SAAS,CAAC0B,OAAO,CAAC,SAAUP,QAAQ,EAAE;QACrC,MAAME,MAAM,GAAGjB,QAAQ,CAACkB,SAAS,CAACH,QAAQ,CAAC;QAC3CE,MAAM,CAACzB,KAAK,CAACM,OAAO,EAAEiB,QAAQ,CAAC;KAC/B,CAAC;CACF"}