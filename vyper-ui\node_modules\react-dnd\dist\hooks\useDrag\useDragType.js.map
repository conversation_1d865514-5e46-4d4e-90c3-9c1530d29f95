{"version": 3, "sources": ["../../../src/hooks/useDrag/useDragType.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { Identifier } from 'dnd-core'\nimport { useMemo } from 'react'\n\nimport type { DragSourceHookSpec } from '../types.js'\n\nexport function useDragType(\n\tspec: DragSourceHookSpec<any, any, any>,\n): Identifier {\n\treturn useMemo(() => {\n\t\tconst result: Identifier = spec.type\n\t\tinvariant(result != null, 'spec.type must be defined')\n\t\treturn result\n\t}, [spec])\n}\n"], "names": ["invariant", "useMemo", "useDragType", "spec", "result", "type"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAEhD,SAASC,OAAO,QAAQ,OAAO,CAAA;AAI/B,OAAO,SAASC,WAAW,CAC1BC,IAAuC,EAC1B;IACb,OAAOF,OAAO,CAAC,IAAM;QACpB,MAAMG,MAAM,GAAeD,IAAI,CAACE,IAAI;QACpCL,SAAS,CAACI,MAAM,IAAI,IAAI,EAAE,2BAA2B,CAAC;QACtD,OAAOA,MAAM,CAAA;KACb,EAAE;QAACD,IAAI;KAAC,CAAC,CAAA;CACV"}