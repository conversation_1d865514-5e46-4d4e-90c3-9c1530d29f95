{"version": 3, "sources": ["../../src/core/DragPreviewImage.ts"], "sourcesContent": ["import type { FC } from 'react'\nimport { memo, useEffect } from 'react'\n\nimport type { ConnectDragPreview } from '../types/index.js'\n\nexport interface DragPreviewImageProps {\n\tconnect: ConnectDragPreview\n\tsrc: string\n}\n/**\n * A utility for rendering a drag preview image\n */\nexport const DragPreviewImage: FC<DragPreviewImageProps> = memo(\n\tfunction DragPreviewImage({ connect, src }) {\n\t\tuseEffect(() => {\n\t\t\tif (typeof Image === 'undefined') return\n\n\t\t\tlet connected = false\n\t\t\tconst img = new Image()\n\t\t\timg.src = src\n\t\t\timg.onload = () => {\n\t\t\t\tconnect(img)\n\t\t\t\tconnected = true\n\t\t\t}\n\t\t\treturn () => {\n\t\t\t\tif (connected) {\n\t\t\t\t\tconnect(null)\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\treturn null\n\t},\n)\n"], "names": ["memo", "useEffect", "DragPreviewImage", "connect", "src", "Image", "connected", "img", "onload"], "mappings": "AACA,SAASA,IAAI,EAAEC,SAAS,QAAQ,OAAO,CAAA;AAQvC;;GAEG,CACH,OAAO,MAAMC,gBAAgB,GAA8BF,IAAI,CAC9D,SAASE,gBAAgB,CAAC,EAAEC,OAAO,CAAA,EAAEC,GAAG,CAAA,EAAE,EAAE;IAC3CH,SAAS,CAAC,IAAM;QACf,IAAI,OAAOI,KAAK,KAAK,WAAW,EAAE,OAAM;QAExC,IAAIC,SAAS,GAAG,KAAK;QACrB,MAAMC,GAAG,GAAG,IAAIF,KAAK,EAAE;QACvBE,GAAG,CAACH,GAAG,GAAGA,GAAG;QACbG,GAAG,CAACC,MAAM,GAAG,IAAM;YAClBL,OAAO,CAACI,GAAG,CAAC;YACZD,SAAS,GAAG,IAAI;SAChB;QACD,OAAO,IAAM;YACZ,IAAIA,SAAS,EAAE;gBACdH,OAAO,CAAC,IAAI,CAAC;aACb;SACD,CAAA;KACD,CAAC;IAEF,OAAO,IAAI,CAAA;CACX,CACD,CAAA"}