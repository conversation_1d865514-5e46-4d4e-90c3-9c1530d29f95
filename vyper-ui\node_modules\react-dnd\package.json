{"_from": "react-dnd", "_id": "react-dnd@16.0.1", "_inBundle": false, "_integrity": "sha512-QeoM/i73HHu2XF9aKksIUuamHPDvRglEwdHL4jsp784BgUuWcg6mzfxT0QDdQz8Wj0qyRKx2eMg8iZtWvU4E2Q==", "_location": "/react-dnd", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "react-dnd", "name": "react-dnd", "escapedName": "react-dnd", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/react-dnd/-/react-dnd-16.0.1.tgz", "_shasum": "2442a3ec67892c60d40a1559eef45498ba26fa37", "_spec": "react-dnd", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/react-dnd/react-dnd/issues"}, "bundleDependencies": false, "dependencies": {"@react-dnd/invariant": "^4.0.1", "@react-dnd/shallowequal": "^4.0.1", "dnd-core": "^16.0.1", "fast-deep-equal": "^3.1.3", "hoist-non-react-statics": "^3.3.2"}, "deprecated": false, "description": "Drag and Drop for React", "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.2.168", "@testing-library/react": "^13.1.1", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.25", "@types/react": "^18.0.5", "@types/react-dom": "^18.0.1", "jest": "^27.5.1", "jest-mock": "^27.5.1", "npm-run-all": "^4.1.5", "react": "^18.0.0", "react-dnd-test-backend": "portal:../backend-test", "react-dnd-test-utils": "portal:../test-utils", "react-dom": "^18.0.0", "shx": "^0.3.4", "typescript": "^4.6.3"}, "homepage": "https://github.com/react-dnd/react-dnd#readme", "license": "MIT", "main": "dist/index.js", "name": "react-dnd", "peerDependencies": {"@types/hoist-non-react-statics": ">= 3.3.1", "@types/node": ">= 12", "@types/react": ">= 16", "react": ">= 16.14"}, "peerDependenciesMeta": {"@types/hoist-non-react-statics": {"optional": true}, "@types/node": {"optional": true}, "@types/react": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/react-dnd/react-dnd.git"}, "scripts": {"build": "run-s build_types build_esm", "build_esm": "swc -C module.type=es6 -d dist src/", "build_types": "tsc -b .", "clean": "shx rm -rf dist/"}, "sideEffects": false, "type": "module", "types": "dist/index.d.ts", "version": "16.0.1"}