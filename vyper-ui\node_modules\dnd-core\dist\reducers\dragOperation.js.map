{"version": 3, "sources": ["../../src/reducers/dragOperation.ts"], "sourcesContent": ["import {\n\tBEGIN_DRAG,\n\tDROP,\n\t<PERSON>ND_DRAG,\n\tHOVER,\n\tPUBLISH_DRAG_SOURCE,\n} from '../actions/dragDrop/index.js'\nimport { REMOVE_TARGET } from '../actions/registry.js'\nimport type { Action, Identifier } from '../interfaces.js'\nimport { without } from '../utils/js_utils.js'\n\nexport interface State {\n\titemType: Identifier | Identifier[] | null\n\titem: any\n\tsourceId: string | null\n\ttargetIds: string[]\n\tdropResult: any\n\tdidDrop: boolean\n\tisSourcePublic: boolean | null\n}\n\nconst initialState: State = {\n\titemType: null,\n\titem: null,\n\tsourceId: null,\n\ttargetIds: [],\n\tdropResult: null,\n\tdidDrop: false,\n\tisSourcePublic: null,\n}\n\nexport function reduce(\n\tstate: State = initialState,\n\taction: Action<{\n\t\titemType: Identifier | Identifier[]\n\t\titem: any\n\t\tsourceId: string\n\t\ttargetId: string\n\t\ttargetIds: string[]\n\t\tisSourcePublic: boolean\n\t\tdropResult: any\n\t}>,\n): State {\n\tconst { payload } = action\n\tswitch (action.type) {\n\t\tcase BEGIN_DRAG:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\titemType: payload.itemType,\n\t\t\t\titem: payload.item,\n\t\t\t\tsourceId: payload.sourceId,\n\t\t\t\tisSourcePublic: payload.isSourcePublic,\n\t\t\t\tdropResult: null,\n\t\t\t\tdidDrop: false,\n\t\t\t}\n\t\tcase PUBLISH_DRAG_SOURCE:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tisSourcePublic: true,\n\t\t\t}\n\t\tcase HOVER:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\ttargetIds: payload.targetIds,\n\t\t\t}\n\t\tcase REMOVE_TARGET:\n\t\t\tif (state.targetIds.indexOf(payload.targetId) === -1) {\n\t\t\t\treturn state\n\t\t\t}\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\ttargetIds: without(state.targetIds, payload.targetId),\n\t\t\t}\n\t\tcase DROP:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tdropResult: payload.dropResult,\n\t\t\t\tdidDrop: true,\n\t\t\t\ttargetIds: [],\n\t\t\t}\n\t\tcase END_DRAG:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\titemType: null,\n\t\t\t\titem: null,\n\t\t\t\tsourceId: null,\n\t\t\t\tdropResult: null,\n\t\t\t\tdidDrop: false,\n\t\t\t\tisSourcePublic: null,\n\t\t\t\ttargetIds: [],\n\t\t\t}\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n"], "names": ["BEGIN_DRAG", "DROP", "END_DRAG", "HOVER", "PUBLISH_DRAG_SOURCE", "REMOVE_TARGET", "without", "initialState", "itemType", "item", "sourceId", "targetIds", "dropResult", "didDrop", "isSourcePublic", "reduce", "state", "action", "payload", "type", "indexOf", "targetId"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SACCA,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,mBAAmB,QACb,8BAA8B,CAAA;AACrC,SAASC,aAAa,QAAQ,wBAAwB,CAAA;AAEtD,SAASC,OAAO,QAAQ,sBAAsB,CAAA;AAY9C,MAAMC,YAAY,GAAU;IAC3BC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,KAAK;IACdC,cAAc,EAAE,IAAI;CACpB;AAED,OAAO,SAASC,MAAM,CACrBC,KAAY,GAAGT,YAAY,EAC3BU,MAQE,EACM;IACR,MAAM,EAAEC,OAAO,CAAA,EAAE,GAAGD,MAAM;IAC1B,OAAQA,MAAM,CAACE,IAAI;QAClB,KAAKnB,UAAU;YACd,OAAO,kBACHgB,KAAK;gBACRR,QAAQ,EAAEU,OAAO,CAACV,QAAQ;gBAC1BC,IAAI,EAAES,OAAO,CAACT,IAAI;gBAClBC,QAAQ,EAAEQ,OAAO,CAACR,QAAQ;gBAC1BI,cAAc,EAAEI,OAAO,CAACJ,cAAc;gBACtCF,UAAU,EAAE,IAAI;gBAChBC,OAAO,EAAE,KAAK;cACd,CAAA;QACF,KAAKT,mBAAmB;YACvB,OAAO,kBACHY,KAAK;gBACRF,cAAc,EAAE,IAAI;cACpB,CAAA;QACF,KAAKX,KAAK;YACT,OAAO,kBACHa,KAAK;gBACRL,SAAS,EAAEO,OAAO,CAACP,SAAS;cAC5B,CAAA;QACF,KAAKN,aAAa;YACjB,IAAIW,KAAK,CAACL,SAAS,CAACS,OAAO,CAACF,OAAO,CAACG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrD,OAAOL,KAAK,CAAA;aACZ;YACD,OAAO,kBACHA,KAAK;gBACRL,SAAS,EAAEL,OAAO,CAACU,KAAK,CAACL,SAAS,EAAEO,OAAO,CAACG,QAAQ,CAAC;cACrD,CAAA;QACF,KAAKpB,IAAI;YACR,OAAO,kBACHe,KAAK;gBACRJ,UAAU,EAAEM,OAAO,CAACN,UAAU;gBAC9BC,OAAO,EAAE,IAAI;gBACbF,SAAS,EAAE,EAAE;cACb,CAAA;QACF,KAAKT,QAAQ;YACZ,OAAO,kBACHc,KAAK;gBACRR,QAAQ,EAAE,IAAI;gBACdC,IAAI,EAAE,IAAI;gBACVC,QAAQ,EAAE,IAAI;gBACdE,UAAU,EAAE,IAAI;gBAChBC,OAAO,EAAE,KAAK;gBACdC,cAAc,EAAE,IAAI;gBACpBH,SAAS,EAAE,EAAE;cACb,CAAA;QACF;YACC,OAAOK,KAAK,CAAA;KACb;CACD"}