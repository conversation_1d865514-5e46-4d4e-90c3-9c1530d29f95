{"version": 3, "sources": ["../../../src/actions/dragDrop/index.ts"], "sourcesContent": ["import type { DragDropActions, DragDropManager } from '../../interfaces.js'\nimport { createBeginDrag } from './beginDrag.js'\nimport { createDrop } from './drop.js'\nimport { createEndDrag } from './endDrag.js'\nimport { createHover } from './hover.js'\nimport { createPublishDragSource } from './publishDragSource.js'\n\nexport * from './types.js'\n\nexport function createDragDropActions(\n\tmanager: DragDropManager,\n): DragDropActions {\n\treturn {\n\t\tbeginDrag: createBeginDrag(manager),\n\t\tpublishDragSource: createPublishDragSource(manager),\n\t\thover: createHover(manager),\n\t\tdrop: createDrop(manager),\n\t\tendDrag: createEndDrag(manager),\n\t}\n}\n"], "names": ["createBeginDrag", "createDrop", "createEndDrag", "createHover", "createPublishDragSource", "createDragDropActions", "manager", "beginDrag", "publishDragSource", "hover", "drop", "endDrag"], "mappings": "AACA,SAASA,eAAe,QAAQ,gBAAgB,CAAA;AAChD,SAASC,UAAU,QAAQ,WAAW,CAAA;AACtC,SAASC,aAAa,QAAQ,cAAc,CAAA;AAC5C,SAASC,WAAW,QAAQ,YAAY,CAAA;AACxC,SAASC,uBAAuB,QAAQ,wBAAwB,CAAA;AAEhE,cAAc,YAAY,CAAA;AAE1B,OAAO,SAASC,qBAAqB,CACpCC,OAAwB,EACN;IAClB,OAAO;QACNC,SAAS,EAAEP,eAAe,CAACM,OAAO,CAAC;QACnCE,iBAAiB,EAAEJ,uBAAuB,CAACE,OAAO,CAAC;QACnDG,KAAK,EAAEN,WAAW,CAACG,OAAO,CAAC;QAC3BI,IAAI,EAAET,UAAU,CAACK,OAAO,CAAC;QACzBK,OAAO,EAAET,aAAa,CAACI,OAAO,CAAC;KAC/B,CAAA;CACD"}