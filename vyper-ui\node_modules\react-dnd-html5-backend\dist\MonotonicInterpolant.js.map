{"version": 3, "sources": ["../src/MonotonicInterpolant.ts"], "sourcesContent": ["export class MonotonicInterpolant {\n\tprivate xs: any\n\tprivate ys: any\n\tprivate c1s: any\n\tprivate c2s: any\n\tprivate c3s: any\n\n\tpublic constructor(xs: number[], ys: number[]) {\n\t\tconst { length } = xs\n\n\t\t// Rearrange xs and ys so that xs is sorted\n\t\tconst indexes = []\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tindexes.push(i)\n\t\t}\n\t\tindexes.sort((a, b) => ((xs[a] as number) < (xs[b] as number) ? -1 : 1))\n\n\t\t// Get consecutive differences and slopes\n\t\tconst dys = []\n\t\tconst dxs = []\n\t\tconst ms = []\n\t\tlet dx\n\t\tlet dy\n\t\tfor (let i = 0; i < length - 1; i++) {\n\t\t\tdx = (xs[i + 1] as number) - (xs[i] as number)\n\t\t\tdy = (ys[i + 1] as number) - (ys[i] as number)\n\t\t\tdxs.push(dx)\n\t\t\tdys.push(dy)\n\t\t\tms.push(dy / dx)\n\t\t}\n\n\t\t// Get degree-1 coefficients\n\t\tconst c1s = [ms[0]]\n\t\tfor (let i = 0; i < dxs.length - 1; i++) {\n\t\t\tconst m2 = ms[i] as number\n\t\t\tconst mNext = ms[i + 1] as number\n\t\t\tif (m2 * mNext <= 0) {\n\t\t\t\tc1s.push(0)\n\t\t\t} else {\n\t\t\t\tdx = dxs[i] as number\n\t\t\t\tconst dxNext = dxs[i + 1] as number\n\t\t\t\tconst common = dx + dxNext\n\t\t\t\tc1s.push(\n\t\t\t\t\t(3 * common) / ((common + dxNext) / m2 + (common + dx) / mNext),\n\t\t\t\t)\n\t\t\t}\n\t\t}\n\t\tc1s.push(ms[ms.length - 1])\n\n\t\t// Get degree-2 and degree-3 coefficients\n\t\tconst c2s = []\n\t\tconst c3s = []\n\t\tlet m\n\t\tfor (let i = 0; i < c1s.length - 1; i++) {\n\t\t\tm = ms[i] as number\n\t\t\tconst c1 = c1s[i] as number\n\t\t\tconst invDx = 1 / (dxs[i] as number)\n\t\t\tconst common = c1 + (c1s[i + 1] as number) - m - m\n\t\t\tc2s.push((m - c1 - common) * invDx)\n\t\t\tc3s.push(common * invDx * invDx)\n\t\t}\n\n\t\tthis.xs = xs\n\t\tthis.ys = ys\n\t\tthis.c1s = c1s\n\t\tthis.c2s = c2s\n\t\tthis.c3s = c3s\n\t}\n\n\tpublic interpolate(x: number): number {\n\t\tconst { xs, ys, c1s, c2s, c3s } = this\n\n\t\t// The rightmost point in the dataset should give an exact result\n\t\tlet i = xs.length - 1\n\t\tif (x === xs[i]) {\n\t\t\treturn ys[i]\n\t\t}\n\n\t\t// Search for the interval x is in, returning the corresponding y if x is one of the original xs\n\t\tlet low = 0\n\t\tlet high = c3s.length - 1\n\t\tlet mid\n\t\twhile (low <= high) {\n\t\t\tmid = Math.floor(0.5 * (low + high))\n\t\t\tconst xHere = xs[mid]\n\t\t\tif (xHere < x) {\n\t\t\t\tlow = mid + 1\n\t\t\t} else if (xHere > x) {\n\t\t\t\thigh = mid - 1\n\t\t\t} else {\n\t\t\t\treturn ys[mid]\n\t\t\t}\n\t\t}\n\t\ti = Math.max(0, high)\n\n\t\t// Interpolate\n\t\tconst diff = x - xs[i]\n\t\tconst diffSq = diff * diff\n\t\treturn ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq\n\t}\n}\n"], "names": ["MonotonicInterpolant", "interpolate", "x", "xs", "ys", "c1s", "c2s", "c3s", "i", "length", "low", "high", "mid", "Math", "floor", "xHere", "max", "diff", "diffSq", "indexes", "push", "sort", "a", "b", "dys", "dxs", "ms", "dx", "dy", "m2", "mNext", "dxNext", "common", "m", "c1", "invDx"], "mappings": "AAAA,OAAO,MAAMA,oBAAoB;IAqEhC,AAAOC,WAAW,CAACC,CAAS,EAAU;QACrC,MAAM,EAAEC,EAAE,CAAA,EAAEC,EAAE,CAAA,EAAEC,GAAG,CAAA,EAAEC,GAAG,CAAA,EAAEC,GAAG,CAAA,EAAE,GAAG,IAAI;QAEtC,iEAAiE;QACjE,IAAIC,CAAC,GAAGL,EAAE,CAACM,MAAM,GAAG,CAAC;QACrB,IAAIP,CAAC,KAAKC,EAAE,CAACK,CAAC,CAAC,EAAE;YAChB,OAAOJ,EAAE,CAACI,CAAC,CAAC,CAAA;SACZ;QAED,gGAAgG;QAChG,IAAIE,GAAG,GAAG,CAAC;QACX,IAAIC,IAAI,GAAGJ,GAAG,CAACE,MAAM,GAAG,CAAC;QACzB,IAAIG,GAAG;QACP,MAAOF,GAAG,IAAIC,IAAI,CAAE;YACnBC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,CAACJ,GAAG,GAAGC,IAAI,CAAC,CAAC;YACpC,MAAMI,KAAK,GAAGZ,EAAE,CAACS,GAAG,CAAC;YACrB,IAAIG,KAAK,GAAGb,CAAC,EAAE;gBACdQ,GAAG,GAAGE,GAAG,GAAG,CAAC;aACb,MAAM,IAAIG,KAAK,GAAGb,CAAC,EAAE;gBACrBS,IAAI,GAAGC,GAAG,GAAG,CAAC;aACd,MAAM;gBACN,OAAOR,EAAE,CAACQ,GAAG,CAAC,CAAA;aACd;SACD;QACDJ,CAAC,GAAGK,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEL,IAAI,CAAC;QAErB,cAAc;QACd,MAAMM,IAAI,GAAGf,CAAC,GAAGC,EAAE,CAACK,CAAC,CAAC;QACtB,MAAMU,MAAM,GAAGD,IAAI,GAAGA,IAAI;QAC1B,OAAOb,EAAE,CAACI,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC,GAAGS,IAAI,GAAGX,GAAG,CAACE,CAAC,CAAC,GAAGU,MAAM,GAAGX,GAAG,CAACC,CAAC,CAAC,GAAGS,IAAI,GAAGC,MAAM,CAAA;KACvE;IA5FD,YAAmBf,EAAY,EAAEC,EAAY,CAAE;QAC9C,MAAM,EAAEK,MAAM,CAAA,EAAE,GAAGN,EAAE;QAErB,2CAA2C;QAC3C,MAAMgB,OAAO,GAAG,EAAE;QAClB,IAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,CAAE;YAChCW,OAAO,CAACC,IAAI,CAACZ,CAAC,CAAC;SACf;QACDW,OAAO,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAM,AAACpB,EAAE,CAACmB,CAAC,CAAC,GAAenB,EAAE,CAACoB,CAAC,CAAC,AAAW,GAAG,CAAC,CAAC,GAAG,CAAC;QAAC,CAAC;QAExE,yCAAyC;QACzC,MAAMC,GAAG,GAAG,EAAE;QACd,MAAMC,GAAG,GAAG,EAAE;QACd,MAAMC,EAAE,GAAG,EAAE;QACb,IAAIC,EAAE;QACN,IAAIC,EAAE;QACN,IAAK,IAAIpB,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGC,MAAM,GAAG,CAAC,EAAED,EAAC,EAAE,CAAE;YACpCmB,EAAE,GAAG,AAACxB,EAAE,CAACK,EAAC,GAAG,CAAC,CAAC,GAAeL,EAAE,CAACK,EAAC,CAAC,AAAW;YAC9CoB,EAAE,GAAG,AAACxB,EAAE,CAACI,EAAC,GAAG,CAAC,CAAC,GAAeJ,EAAE,CAACI,EAAC,CAAC,AAAW;YAC9CiB,GAAG,CAACL,IAAI,CAACO,EAAE,CAAC;YACZH,GAAG,CAACJ,IAAI,CAACQ,EAAE,CAAC;YACZF,EAAE,CAACN,IAAI,CAACQ,EAAE,GAAGD,EAAE,CAAC;SAChB;QAED,4BAA4B;QAC5B,MAAMtB,GAAG,GAAG;YAACqB,EAAE,CAAC,CAAC,CAAC;SAAC;QACnB,IAAK,IAAIlB,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGiB,GAAG,CAAChB,MAAM,GAAG,CAAC,EAAED,EAAC,EAAE,CAAE;YACxC,MAAMqB,EAAE,GAAGH,EAAE,CAAClB,EAAC,CAAC,AAAU;YAC1B,MAAMsB,KAAK,GAAGJ,EAAE,CAAClB,EAAC,GAAG,CAAC,CAAC,AAAU;YACjC,IAAIqB,EAAE,GAAGC,KAAK,IAAI,CAAC,EAAE;gBACpBzB,GAAG,CAACe,IAAI,CAAC,CAAC,CAAC;aACX,MAAM;gBACNO,EAAE,GAAGF,GAAG,CAACjB,EAAC,CAAC,AAAU;gBACrB,MAAMuB,MAAM,GAAGN,GAAG,CAACjB,EAAC,GAAG,CAAC,CAAC,AAAU;gBACnC,MAAMwB,MAAM,GAAGL,EAAE,GAAGI,MAAM;gBAC1B1B,GAAG,CAACe,IAAI,CACP,AAAC,CAAC,GAAGY,MAAM,GAAI,CAAC,CAACA,MAAM,GAAGD,MAAM,CAAC,GAAGF,EAAE,GAAG,CAACG,MAAM,GAAGL,EAAE,CAAC,GAAGG,KAAK,CAAC,CAC/D;aACD;SACD;QACDzB,GAAG,CAACe,IAAI,CAACM,EAAE,CAACA,EAAE,CAACjB,MAAM,GAAG,CAAC,CAAC,CAAC;QAE3B,yCAAyC;QACzC,MAAMH,GAAG,GAAG,EAAE;QACd,MAAMC,GAAG,GAAG,EAAE;QACd,IAAI0B,CAAC;QACL,IAAK,IAAIzB,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGH,GAAG,CAACI,MAAM,GAAG,CAAC,EAAED,EAAC,EAAE,CAAE;YACxCyB,CAAC,GAAGP,EAAE,CAAClB,EAAC,CAAC,AAAU;YACnB,MAAM0B,EAAE,GAAG7B,GAAG,CAACG,EAAC,CAAC,AAAU;YAC3B,MAAM2B,KAAK,GAAG,CAAC,GAAIV,GAAG,CAACjB,EAAC,CAAC,AAAW;YACpC,MAAMwB,MAAM,GAAGE,EAAE,GAAI7B,GAAG,CAACG,EAAC,GAAG,CAAC,CAAC,GAAcyB,CAAC,GAAGA,CAAC;YAClD3B,GAAG,CAACc,IAAI,CAAC,CAACa,CAAC,GAAGC,EAAE,GAAGF,MAAM,CAAC,GAAGG,KAAK,CAAC;YACnC5B,GAAG,CAACa,IAAI,CAACY,MAAM,GAAGG,KAAK,GAAGA,KAAK,CAAC;SAChC;QAED,IAAI,CAAChC,EAAE,GAAGA,EAAE;QACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;QACZ,IAAI,CAACC,GAAG,GAAGA,GAAG;QACd,IAAI,CAACC,GAAG,GAAGA,GAAG;QACd,IAAI,CAACC,GAAG,GAAGA,GAAG;KACd;CAiCD"}