{"version": 3, "sources": ["../../src/NativeDragSources/getDataFromDataTransfer.ts"], "sourcesContent": ["export function getDataFromDataTransfer(\n\tdataTransfer: DataTransfer,\n\ttypesToTry: string[],\n\tdefaultValue: string,\n): string {\n\tconst result = typesToTry.reduce(\n\t\t(resultSoFar, typeToTry) => resultSoFar || dataTransfer.getData(typeToTry),\n\t\t'',\n\t)\n\n\treturn result != null ? result : defaultValue\n}\n"], "names": ["getDataFromDataTransfer", "dataTransfer", "typesToTry", "defaultValue", "result", "reduce", "resultSoFar", "typeToTry", "getData"], "mappings": "AAAA,OAAO,SAASA,uBAAuB,CACtCC,YAA0B,EAC1BC,UAAoB,EACpBC,YAAoB,EACX;IACT,MAAMC,MAAM,GAAGF,UAAU,CAACG,MAAM,CAC/B,CAACC,WAAW,EAAEC,SAAS,GAAKD,WAAW,IAAIL,YAAY,CAACO,OAAO,CAACD,SAAS,CAAC;IAAA,EAC1E,EAAE,CACF;IAED,OAAOH,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGD,YAAY,CAAA;CAC7C"}