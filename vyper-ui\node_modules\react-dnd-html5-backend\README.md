[![npm package](https://img.shields.io/npm/v/react-dnd-html5-backend.svg?style=flat-square)](https://www.npmjs.org/package/react-dnd-html5-backend)
[![Build Status](https://travis-ci.org/react-dnd/react-dnd-html5-backend.svg?branch=main)](https://travis-ci.org/react-dnd/react-dnd-html5-backend)
[![dependencies Status](https://david-dm.org/react-dnd/react-dnd-html5-backend/status.svg)](https://david-dm.org/react-dnd/react-dnd-html5-backend)
[![devDependencies Status](https://david-dm.org/react-dnd/react-dnd-html5-backend/dev-status.svg)](https://david-dm.org/react-dnd/react-dnd-html5-backend?type=dev)
[![peerDependencies Status](https://david-dm.org/react-dnd/react-dnd-html5-backend/peer-status.svg)](https://david-dm.org/react-dnd/react-dnd-html5-backend?type=peer)

# React DnD HTML5 Backend

The officially supported HTML5 backend for [React DnD](http://react-dnd.github.io/react-dnd/).
See [the docs](http://react-dnd.github.io/react-dnd/docs/backends/html5) for usage information.

## Installation

If you use [npm](http://npmjs.com):

```
npm install --save react-dnd-html5-backend
```

## Browser Support

We strive to support the evergreen browsers.

Unfortunately the browser bugs, inconsistencies, and regressions come up from time to time, so please make sure you test your app on the browsers you’re interested in, and report any bugs to us.

## License

MIT
